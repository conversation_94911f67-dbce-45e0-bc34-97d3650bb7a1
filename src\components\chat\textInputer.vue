<template>
  <div ref="chatInputBoxRef" class="input-box">
    <div class="input-container">
      <div v-if="displayOption.attachShow" class="input-attach">
        <Attach :files="cacheFiles" @remove="removeFile"></Attach>
      </div>
      <div class="input-inner inner-default inner-small">
        <div class="inner-textarea-wrap" style="position: relative; padding-top: 0.75rem">
          <div class="inner-text-content">
            <el-input v-model="inputValue" :placeholder="$t('请输入法律相关问题进行问答')" type="textarea"></el-input>
          </div>
        </div>
      </div>
      <div v-if="displayOption.uploadShow" class="input-upload"></div>
      <div v-if="displayOption.leftFooterBarShow" class="input-footer-left">
        <div>
          <div class="chat-ai-search-btns">
            <div
              :class="{ btn: true, 'btn-active': isDeepSeek }"
              :style="{ color: isDeepSeek || deepSeekHover ? 'var(--is-color-773bef)' : '' }"
              @click="setDeepseek"
              @mouseenter="deepSeekHover = true"
              @mouseleave="deepSeekHover = false"
            >
              <span>
                <i class="iconfont">&#xe63f;</i>
              </span>
              <span>{{ $t('深度思考(R1)') }}</span>
            </div>
            <div
              :class="{ btn: true, 'btn-active': isWebSearch }"
              :style="{ color: isWebSearch || internetSearchHover ? 'var(--is-color-773bef)' : '' }"
              @click="setNetSearch"
              @mouseenter="internetSearchHover = true"
              @mouseleave="internetSearchHover = false"
            >
              <span> <i class="iconfont">&#xe65e;</i></span>
              <span>{{ $t('联网搜索') }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="input-footer-right">
        <div>
          <div v-if="cacheFiles.length < CHAT_ATTACH_LIMIT_COUNT" class="input-send-btn">
            <el-tooltip class="box-item" effect="dark" :content="$t('支持png、jpeg、jpg、pdf、docx等')" placement="top">
              <div>
                <el-upload
                  ref="chatUpload"
                  :file-list="uploadFiles"
                  :multiple="true"
                  :show-file-list="false"
                  :accept="CHAT_ATTACH_ACCEPT"
                  :http-request="customUpload"
                  :before-upload="beforeUpload"
                  :limit="CHAT_ATTACH_LIMIT_COUNT"
                  style="display: none"
                >
                </el-upload>
                <div class="chat-upload-cover">
                  <i class="iconfont" style="font-size: 2rem" @click="handleUploadClick">&#xe664;</i>
                </div>
              </div>
            </el-tooltip>
          </div>
          <div v-else style="cursor: not-allowed">
            <i class="iconfont" style="font-size: 2rem">&#xe664;</i>
          </div>
        </div>

        <div v-if="isUnReady" class="input-send-btn not-allowed">
          <i class="iconfont" style="font-size: 2rem; color: var(--is-color-773bef); opacity: 0.5">&#xe67f;</i>
        </div>
        <div v-if="isReady" class="input-send-btn" @click="send">
          <i class="iconfont" style="font-size: 2rem; color: var(--is-color-773bef)">&#xe67f;</i>
        </div>
        <div v-if="isStopable" class="input-send-btn" @click="stop" color="#aeaeae">
          <el-tooltip class="box-item" effect="dark" :content="$t('点击终止生成')" placement="top">
            <i class="iconfont" style="font-size: 2rem; color: var(--is-color-773bef)">&#xe683;</i>
          </el-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import Attach from '@/components/chat/attach.vue'
import { InputerStyleEnum } from '@/constants'
import { useTextInputerService } from './useTextInputer'
import type { UploadRawFile } from 'element-plus'
import { CHAT_ATTACH_ACCEPT, CHAT_ATTACH_LIMIT_SIZE, CHAT_ATTACH_LIMIT_COUNT, BtnStatusEnum } from '@/constants'
import { useUserStore } from '@/stores'
import { $t } from '@/utils/i18n'

const {
  inputValue,
  cacheFiles,
  displayOption,
  isReady,
  isUnReady,
  isStopable,
  chatInputBoxRef,
  btnStatus,
  isDeepSeek,
  isWebSearch,
  send,
  stop,
  clearParams,
  setContainerSty,
  uploadFiles,
  customUpload,
  removeFile,
} = useTextInputerService()
const userStore = useUserStore()
const emits = defineEmits(['send', 'stop', 'close', 'size-change'])
const chatUpload = ref<any>(null)
const deepSeekHover = ref(false)
const internetSearchHover = ref(false)
defineProps({
  size: {
    type: String,
    default() {
      return InputerStyleEnum.ATTACH
    },
  },
  hook: {
    type: [Function],
    default() {
      return () => {}
    },
  },
  width: {
    type: Number,
    default() {
      return 800 * 0.9
    },
  },
  innerheight: {
    type: Number,
    default() {
      return 68
    },
  },
  labelVisable: {
    type: Boolean,
    default() {
      return true
    },
  },
})

const handleUploadClick = () => {
  if (!userStore.getToken) {
    userStore.setShowLoginBox(true)
    return
  }
  // token 存在，手动触发 el-upload 的隐藏 input
  const inputEl = chatUpload.value?.$el.querySelector('input[type="file"]') as HTMLInputElement | null
  if (inputEl) {
    inputEl.click()
  } else {
    ElMessage.error($t('出现异常，请稍后再试'))
  }
}

const beforeUpload = (rawFile: UploadRawFile) => {
  const size = rawFile.size / (1024 * 1024)
  if (size > CHAT_ATTACH_LIMIT_SIZE) {
    ElMessage.warning(`单个文件大小${CHAT_ATTACH_LIMIT_SIZE}M以内`)
    return false
  }
}

const setDeepseek = () => {
  isDeepSeek.value = !isDeepSeek.value
}

const setNetSearch = () => {
  isWebSearch.value = !isWebSearch.value
}

defineExpose({
  setContent(content: string) {
    inputValue.value = content
  },
  getContent() {
    return inputValue.value
  },
  setOption(key: InputerStyleEnum) {
    setContainerSty(key)
  },
  setBtnStatus(status: BtnStatusEnum) {
    btnStatus.value = status
  },
  reset() {
    clearParams()
  },
  getBtnStatus() {
    return btnStatus.value
  },
  setIsDeepSeek(isDS: boolean) {
    isDeepSeek.value = isDS
  },
  setIsInternetSearch(isWS: boolean) {
    isWebSearch.value = isWS
  },
})
</script>
<style lang="scss" scoped>
:deep(.el-textarea__inner) {
  scrollbar-gutter: auto;
  resize: none; /* 禁止右下角 resize 拖拽 */
  outline: none; /* 去除聚焦时的轮廓 */
  scrollbar-color: #c1c1c1 transparent; /* 滚动条颜色 轨道颜色 */
  scrollbar-width: thin; /* 细滚动条 */
  border: none; /* 去除默认边框 */
  box-shadow: none; /* 去除默认阴影 */
}
.not-allowed {
  cursor: not-allowed !important;
}
.div-flex {
  display: flex;
  align-items: center;
  margin-left: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: rgb(33 40 103 / 100%);
}
.input-box {
  position: relative;
  z-index: 99;
  width: 100% !important;
  background-color: var(--bg-color);
  border: 1px solid rgb(14 24 106 / 10%);
  border-radius: 0.75rem;
  box-shadow: 0 4px 12px 0 rgb(191 205 237 / 20%);

  --input-box-border-color: rgb(14 24 106 / 10%);
  .input-container {
    --input-inner-height: 4.25rem;

    display: grid;
    grid-template: auto / auto 1fr auto;
    grid-template-areas: ' input-inner . input-footer-right';
    align-items: center;
    width: 100%;
    .input-header {
      display: flex;
      grid-area: input-header;
      align-items: center;
      height: 2.625rem;
      padding: 0 1rem;
      color: rgb(33 40 103 / 100%);
      background: rgb(246 247 249 / 100%);
      border-radius: 0.75rem 0.75rem 0 0;
      .input-header-title {
        display: flex;
        flex: 1 0;
        align-items: center;
      }
    }
    .input-attach {
      grid-area: input-attach;
    }
    .input-upload {
      display: flex;
      grid-area: input-upload;
      justify-content: center;
    }
    .input-inner {
      grid-area: input-inner;
      width: 100%;
    }
    .input-line {
      display: flex;
      grid-area: input-line;
      width: 100%;
      height: 1px;
      margin-top: 12px;
      border-top: 1.5px solid rgb(14 24 106 / 10%);
    }
    .input-footer-left {
      display: flex;
      grid-area: input-footer-left;
      align-items: center;
      justify-content: end;
      padding: 0.75rem;
    }
    .input-footer-right {
      display: flex;
      grid-area: input-footer-right;
      align-items: center;
      justify-content: end;
      padding-right: 0.75rem;
    }
  }
}
.input-send-btn {
  width: 2rem;
  height: 2rem;
  margin-left: 0.5rem;
  cursor: pointer;
  .chat-upload-cover {
    width: 2rem;
    height: 2rem;
  }
  .file-uploads {
    :deep(label) {
      cursor: pointer;
    }
  }
}
.inner-default {
  .inner-textarea-wrap {
    display: flex;
    align-items: center;
    width: 100% !important;
    padding: 0 12px;
    .inner-text-content {
      width: 100% !important;
      overflow-y: auto;
      font-size: 1rem;
      font-weight: 400;
      line-height: 1.5rem;
      color: rgb(0 0 0 / 88%);
    }
  }
}
.inner-small {
  display: flex;
  height: var(--input-inner-height);
  .inner-textarea-wrap {
    height: 100%;
    .inner-text-content {
      box-sizing: content-box;
      display: flex;
      height: 100%;
      .input-placeholder {
        top: 1rem;
        left: 1rem;
      }
    }
  }
}
.chat-input-editor {
  flex: 1 1 auto;
  align-items: center;
  width: fit-content;
  min-width: 6.25rem;
  min-height: 1rem;
  overflow: hidden auto;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1rem;
  text-align: left;
  word-break: break-all;
  word-wrap: break-word;
  white-space: pre-wrap;
  caret-color: #3348ff;
  outline: none;
}
.input-placeholder {
  position: absolute;
  left: 1rem;
  min-width: 500px;
  font-size: 0.875rem;
  color: rgb(0 10 26 / 26%);
}
.input_active {
  --input-box-border-color: #3348ff;
}
.chat-ai-search-btns {
  display: flex;
  align-items: center;
  .btn {
    display: flex;
    align-items: center;
    padding: 0.375rem 0.5rem;
    margin-right: 0.5rem;
    color: var(--minor-font);
    cursor: pointer;
    background: var(--bg-color);
    border: 1px solid rgb(232 232 232 / 100%);
    border-radius: 0.5rem;
    > span:first-child {
      display: flex;
      margin-right: 0.25rem;
    }
    > span:last-child {
      font-size: 0.875rem;
      font-weight: 400;
    }
  }
  .btn:hover {
    color: var(--main-font);
    background-color: var(--primary-btn-bg);
    border: 1px solid var(--primary-btn-bg);
  }
  .btn-active {
    background-color: var(--primary-btn-bg);
    border: 1px solid var(--main-font);
    > span {
      color: var(--main-font);
    }
  }
}
</style>
