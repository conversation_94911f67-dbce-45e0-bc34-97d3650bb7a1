<script setup lang="ts">
import UploadFile from './components/UploadFile.vue'
import RecentBox from './components/RecentBox.vue'
import { compareDiffFile } from '@/services/textComparison'

import { $t } from '@/utils/i18n'
const loading = ref(false)
const maxFile = 100 // M
const sourceFile = ref<Blob>()
const targetFile = ref<Blob>()
const checkList = ref(['ignoreSymbol', 'ignoreHeadersAndFooters', 'ignoreWatermark'])
const canClick = computed(() => !sourceFile.value || !targetFile.value || loading.value)

enum UploadEnum {
  SOURCE = 'source',
  TARGET = 'target',
}
function handleUpload(type: UploadEnum, res: Blob) {
  if (type === UploadEnum.SOURCE) {
    sourceFile.value = res
  } else if (type === UploadEnum.TARGET) {
    targetFile.value = res
  }
}

async function startDiff() {
  if (!sourceFile.value) return ElMessage.error($t('请先上传原稿文档'))
  if (!targetFile.value) return ElMessage.error($t('请先上传比对文档'))

  const data = new FormData()
  data.append('source', sourceFile.value)
  data.append('target', targetFile.value)
  checkList.value.forEach((item) => {
    data.append(item, 'true')
  })
  loading.value = true
  try {
    const res = await compareDiffFile<string>(data)
    window.open(`/comparison?compareId=${res.data}`)
  } catch (error) {
    ElMessage.error($t('比对失败，请稍后重试'))
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="page-wrap">
    <div class="contain">
      <UploadFile
        ref="source"
        :source-type="1"
        :title="$t('原稿文档')"
        :max-file="maxFile"
        @afterUpload="(e: Blob) => handleUpload(UploadEnum.SOURCE, e)"
      />
      <div class="vs"></div>
      <UploadFile
        ref="target"
        :source-type="2"
        :title="$t('改动文档')"
        :max-file="maxFile"
        @afterUpload="(e: Blob) => handleUpload(UploadEnum.TARGET, e)"
      />
      <div class="options-wrap" align="right">
        <el-checkbox-group v-model="checkList">
          <!-- <el-checkbox label="ignoreSymbol">{{ $t('忽略标点符号') }}</el-checkbox> -->
          <!-- <el-checkbox label="ignoreHeadersAndFooters">{{ $t('忽略页眉、页脚、页码') }}</el-checkbox> -->
          <!-- <el-checkbox label="ignoreWatermark">{{ $t('忽略水印') }}</el-checkbox> -->
        </el-checkbox-group>
      </div>
    </div>
    <div class="start-diff" align="center">
      <el-button :loading="loading" class="btn" type="primary" :disabled="canClick" @click="startDiff">{{
        $t('开始比对')
      }}</el-button>
    </div>
    <RecentBox />
  </div>
</template>

<style lang="scss" scoped>
.page-wrap {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  .start-diff {
    width: 1168px;
    margin: 56px auto 0;
    :deep(.el-button) {
      padding: 13px 75px;
      font-size: 18px;
      color: var(--bg-color);
      letter-spacing: 1px;
    }
  }
  .contain {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    min-width: 1192px;

    // margin: auto;
  }
  .options-wrap {
    position: absolute;
    bottom: -35px;
    left: calc(50% - 24px);
    width: 50%;
    padding-top: 20px;
  }
}
.btn {
  width: 420px;
  height: 48px;
}
.vs {
  width: 60px;
  height: 60px;
  margin: 60px 20px 0;
  background: url('@/assets/images/textComparison/vs.svg') 0 0 no-repeat;
  background-size: 100% 100%;
}
</style>
