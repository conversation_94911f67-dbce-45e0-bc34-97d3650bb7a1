<script lang="ts" setup>
import { useContractReviewService } from './useContractReviewService'
import ReviewRuleTree from './ReviewRuleTree.vue'
import TextOverTip from '@/components/TextOverTip.vue'
import TaskLoading from './TaskLoading.vue'
import TaskLoadingDialog from './TaskLoadingDialog.vue'
import { ContractInfo, EBusEvents, ReviewRuleTypeItem, RuleTypeList, type ContractParticipant } from './Model'
import mitt from '@/utils/eventsBus'
import { useContractStore } from '@/stores'
import { getAiProgress, getAllRuleList } from '@/services/contract'
import { $t } from '@/utils/i18n'
const taskLoadingDialogRef = ref()
const contractStore = useContractStore()
const {
  isActiveAble,
  reviewRuleCode,
  currentRulePositionCode,
  reviewPositionCode,
  reviewRuleDatas,
  reviewRuleLoading,
  reviewPositionDatas,
  purposeLoading,
  participantCache,
  reviewPositionLoading,
  ruleTreeData,
  isAiReview,
  customRuleTreeData,
  aiReviewPosition,
  save,
  reloadCustomRule,
  handleReviewRuleChange,
  queryReivewRuleList,
  queryReviewRuleConfig,
  queryAiReviewRulePurposeList,
  handleReviewRulePositionChange,
} = useContractReviewService()

const taskLoadingRef = ref()
const ruleTreeRef = ref()
const isFirstTimeReview = ref(true)
const form = ref({})
const selectedParticipant = ref('')
const isVerifyList = ref(false)

const removePurpose = (participant: ContractParticipant, index: number) => {
  participant.purposes.splice(index, 1)
}
const addPurpose = (participant: ContractParticipant) => {
  participant.purposes.push('')
}

const createSuccess = async () => {
  await reloadCustomRule()
  setTimeout(() => {
    ruleTreeRef.value.scrollToBottom()
  }, 0)
}

const editSuccess = async ({ id, label }: { id: string; label: string }) => {
  customRuleTreeData.value[0].child.forEach((item: ReviewRuleTypeItem) => {
    if (item.id === id) {
      item.ruleTypeName = label
    }
  })
}

const delSuccess = async (id: string) => {
  customRuleTreeData.value[0].child = customRuleTreeData.value[0].child.filter((item: ReviewRuleTypeItem) => {
    return item.id !== id
  })
  if (customRuleTreeData.value[0].child.length === 0) {
    customRuleTreeData.value.length = 0
  }
}

const handleSelectParticipant = (participant: ContractParticipant) => {
  selectedParticipant.value = participant.participantName
  queryAiReviewRulePurposeList(participant)
}

mitt.on(EBusEvents.CONSTRACT_INFO, (info: any) => {
  const contractInfo = info as ContractInfo

  queryReivewRuleList(contractInfo)
})

onBeforeUnmount(() => {
  mitt.off(EBusEvents.CONSTRACT_INFO)
})

const checkAiReview = () => {
  return new Promise((resolve, reject) => {
    if (aiReviewPosition.value!.contractType == '' || !participantCache.value?.reviewPosition) {
      ElMessage.error($t('请选择审查立场'))
      reject(false)
    } else if (!participantCache.value?.purposes || participantCache.value?.purposes.length == 0) {
      ElMessage.error($t('请选择审查目的'))
      reject(false)
    } else {
      resolve(true)
    }
  })
}
const checkPurpose = () => {
  const participants = aiReviewPosition.value?.participants
  if (participants) {
    const current = participants.find((item: ContractParticipant) => item.participantName === selectedParticipant.value)
    if (!current) {
      return false
    }

    // participants.forEach((item: ContractParticipant) => {
    const seen = new Set()
    current.purposes.forEach((purpose: string, index: number) => {
      if (!purpose) {
        current.errs[index] = $t('请填写审查目的')
      } else if (seen.has(purpose)) {
        current.errs[index] = $t('审查目的不能重复')
      } else {
        delete current.errs[index]
        seen.add(purpose)
      }
    })
    // })
    const hasError = participants.find((item) => {
      return item.errs.length > 0
    })

    return new Promise((resolve, reject) => {
      if (!hasError) {
        resolve(true)
      } else {
        reject(false)
      }
    })
  }
}

const ruleTypeList = ref<RuleTypeList[]>([])
const runProcess = async (ruleListId: string) => {
  return await new Promise((resolve) => {
    const interval = setInterval(async () => {
      try {
        const { data: isCompleted } = await getAiProgress(ruleListId)

        if (isCompleted === 1) {
          // 审查成功完成
          const { data } = await getAllRuleList(ruleListId)
          if (data) {
            ruleTypeList.value = (data as { ruleTypeList: RuleTypeList[] }).ruleTypeList
          }

          clearInterval(interval)
          taskLoadingRef.value.stop()

          resolve(true) // 返回 true
        } else if (isCompleted === 2) {
          // 审查清单生成失败
          ElMessage.error($t('审查清单生成失败，请稍后重试！'))
          clearInterval(interval)
          taskLoadingRef.value.stop()

          resolve(false) // 返回 false
        }
      } catch (error) {
        // 发生错误也返回 false
        clearInterval(interval)
        taskLoadingRef.value.stop()

        console.error('AI审查出错:', error)
        resolve(false)
      }
    }, 5000)
  })
}

interface IPostParams {
  contractId: string
  contractName: string
  customLlmReviewRuleIdList?: any
  fileCode: string
  fileUrl: string
  llmReviewRuleIdList?: any
  llmReviewRuleListCode: string
  sourceType: string
}

const checkRule = async () => {
  return new Promise((resolve, reject) => {
    if (!reviewRuleCode.value) {
      ElMessage.error($t('请选择审查清单'))
      reject(false)
    } else if (!reviewPositionCode.value) {
      ElMessage.error($t('请选择审查立场'))
      reject(false)
    } else if (!ruleTreeRef.value) {
      ElMessage.error($t('请选择审查规则'))
    } else {
      resolve(true)
    }
  })
}

const commit = async () => {
  const params: IPostParams = {
    contractId: contractStore.contractInfo.constractId,
    contractName: contractStore.contractInfo.contractName,
    fileCode: contractStore.contractInfo.originalFileCode,
    fileUrl: '',
    sourceType: 'ITERMS',
    llmReviewRuleListCode: '',
  }
  try {
    if (isAiReview.value) {
      await checkAiReview()
      const ruleId = await queryReviewRuleConfig()
      if (isVerifyList.value) {
        taskLoadingDialogRef.value.show({
          fileCode: contractStore.contractInfo.originalFileCode,
          contractName: contractStore.contractInfo.contractName,
          contractType: aiReviewPosition.value!.contractType,
          reviewPosition: participantCache.value!.reviewPosition,
        })
        await runProcess(ruleId)
        taskLoadingDialogRef.value.query(ruleId, ruleTypeList.value)
      } else {
        await checkPurpose()
        taskLoadingRef.value.start()
        await runProcess(ruleId)
        params.customLlmReviewRuleIdList = null
        params.llmReviewRuleListCode = contractStore.contractInfo.ruleCode
        save(params)
      }
    } else {
      await checkRule()
      params.customLlmReviewRuleIdList = []
      if (!reviewRuleCode.value || !reviewPositionCode.value || !ruleTreeRef.value) {
        ElMessage.error($t('请选择审查清单'))
        return
      }
      const keys = await ruleTreeRef.value!.getSelectedKeys()
      params.llmReviewRuleListCode = currentRulePositionCode.value
      params.llmReviewRuleIdList = keys.default
      params.customLlmReviewRuleIdList = keys.custom.filter((it: string) => it != '0')
      console.log(params)
      save(params)
    }
  } catch (e) {
    console.log(e)
    // ElMessage.error($t('请检查审查是否正确'))
  }
}

const onSuccess = async (ruleCode: string) => {
  const params: IPostParams = {
    contractId: contractStore.contractInfo.constractId,
    contractName: contractStore.contractInfo.contractName,
    fileCode: contractStore.contractInfo.originalFileCode,
    fileUrl: '',
    sourceType: 'ITERMS',
    llmReviewRuleListCode: ruleCode,
    llmReviewRuleIdList: null,
  }
  save(params)
}
</script>
<template>
  <div>
    <el-form ref="form" :model="form" label-width="80px">
      <div class="row-item" style="height: calc(100vh - 50px); padding: 1rem">
        <div style="display: flex; align-items: center; justify-content: space-between">
          <div style="display: flex; align-items: center; justify-content: start">
            <span class="custom-label">{{ $t('审查清单') }}</span>
            <el-tooltip
              effect="light"
              v-if="isAiReview"
              :content="$t('未匹配到审查清单，自动勾选“自动生成审查清单”')"
              placement="right"
              :show-arrow="false"
            >
              <i v-if="isAiReview" class="iconfont icon-is-question i-icon" />
            </el-tooltip>
          </div>
          <div style="display: flex; justify-content: end">
            <span>
              <el-checkbox v-model="isAiReview" :disabled="!isActiveAble">{{ $t('自动生成审查清单') }}</el-checkbox>
            </span>
          </div>
        </div>
        <!-- AI生成审查清单-->

        <div style="margin-top: 0.5rem">
          <el-select
            v-model="reviewRuleCode"
            v-loading="reviewRuleLoading"
            :placeholder="isAiReview ? $t('由AI生成') : $t('请选择')"
            :disabled="isAiReview"
            :element-loading-text="$t('合同解析中')"
            filterable
            @change="handleReviewRuleChange"
          >
            <el-option
              v-for="(item, i) in reviewRuleDatas"
              :key="i"
              :label="item.typeName"
              :value="item.typeName"
            ></el-option>
            <template #loading>
              <svg class="circular" style="font-size: 10px" viewBox="0 0 50 50">
                <circle class="path" cx="25" cy="25" r="10" fill="none" />
              </svg>
            </template>
          </el-select>
        </div>

        <div style="display: flex; align-items: center; justify-content: space-between; margin-top: 1rem">
          <div style="display: flex; align-items: center; justify-content: start">
            <span class="custom-label">{{ $t('审查立场') }}</span>
            <el-tooltip
              effect="light"
              v-if="isAiReview"
              :content="$t('审查立场决定审查的利益导向和侧重点')"
              placement="right"
              :show-arrow="false"
            >
              <i v-if="isAiReview" class="iconfont icon-is-question i-icon" />
            </el-tooltip>
          </div>
        </div>
        <!--is normal review-->
        <div v-if="!isAiReview">
          <div style="margin-top: 0.5rem">
            <el-select
              v-model="reviewPositionCode"
              v-loading="reviewPositionLoading"
              :placeholder="$t('请选择')"
              :element-loading-text="$t('合同解析中')"
              @change="handleReviewRulePositionChange"
            >
              <el-option
                v-for="(item, i) in reviewPositionDatas"
                :key="i"
                :label="item.rulePosition"
                :value="item.ruleCode"
              ></el-option>
              <template #loading>
                <svg class="circular" style="font-size: 10px" viewBox="0 0 50 50">
                  <circle class="path" cx="25" cy="25" r="10" fill="none" />
                </svg>
              </template>
            </el-select>
          </div>
          <ReviewRuleTree
            v-if="ruleTreeData.length"
            ref="ruleTreeRef"
            :data="ruleTreeData"
            :custom-data="customRuleTreeData"
            :rule-code="currentRulePositionCode"
            @create="createSuccess"
            @edit="editSuccess"
            @del="delSuccess"
          />
        </div>
        <!---is ai review-->
        <div v-else class="review-participant" style="height: calc(100vh - 287px); overflow: auto">
          <div v-if="aiReviewPosition?.participants.length">
            <el-card
              v-for="(item, index) in aiReviewPosition?.participants"
              :key="index"
              shadow="hover"
              style="margin-top: 1rem; cursor: pointer"
              @click="handleSelectParticipant(item)"
            >
              <div style="display: flex; width: 100%">
                <div style="width: 2rem; margin-top: 5%">
                  <i v-if="selectedParticipant == item.participantName" class="iconfont icon-is-a-11 i-active" />
                  <i v-else class="iconfont icon-is-uncheck-circle-outlined i-icon-normal"></i>
                </div>
                <div style="flex: 1">
                  <div class="participant" :class="{ active: selectedParticipant == item.participantName }">
                    <span class="title"><TextOverTip :content="item.participantName"></TextOverTip> </span>
                    <span class="sub-title">{{ item.reviewPosition }}</span>
                  </div>
                  <div v-show="selectedParticipant == item.participantName" style="min-height: 6rem">
                    <el-divider style="margin: 1rem 0"></el-divider>
                    <div class="purpose-list">
                      <div class="review-purpose">
                        <span>{{ $t('审查目的') }}</span>
                        <el-tooltip
                          effect="light"
                          :content="$t('请明确您的审查目的，这将帮助我们为您提供更精准和有效的审查服务')"
                          placement="right"
                          :show-arrow="false"
                        >
                          <i class="iconfont icon-is-question i-icon" />
                        </el-tooltip>
                      </div>
                      <el-link underline="never" @click.stop="addPurpose(item)" style="color: var(--main-font)">
                        <i
                          class="iconfont icon-is-plus"
                          style="margin-right: 0.25rem; font-size: 0.75rem; color: var(--main-font)"
                        />
                        {{ $t('添加审查目的') }}
                      </el-link>
                    </div>
                    <div
                      v-loading="purposeLoading"
                      :element-loading-text="$t('生成中……')"
                      element-loading-spinner="el-icon-loading"
                    >
                      <div v-for="(purpose, index) in item.purposes" :key="index">
                        <div class="purpose">
                          <div style="flex: 1">
                            <el-input v-model="item.purposes[index]" :placeholder="$t('请输入审查目的')" />
                          </div>
                          <i class="iconfont icon-is-chat-delete i-icon" @click.stop="removePurpose(item, index)" />
                        </div>
                        <div v-if="item.errs" class="error-message">{{ item.errs[index] }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </div>
          <!--TaskLoading-->
          <TaskLoading ref="taskLoadingRef" :hide-header="true" class="task-loading" />
        </div>
        <!---setting-->
        <div v-if="isAiReview" class="review-set">
          <div>
            <span class="title">{{ $t('设置') }}</span>
            <el-tooltip effect="light" placement="right" :show-arrow="false">
              <template #content
                >{{ $t('二次确认审查清单适用于专业使用者，') }}<br />{{
                  $t('开启后可在审查前先对审查清单进行校验确认后，')
                }}<br />{{ $t('再进行审查') }}
              </template>
              <i class="iconfont icon-is-question i-icon" />
            </el-tooltip>
          </div>
          <div class="set-content">
            <span>{{ $t('是否人工确认AI生成的审查清单') }}</span>
            <el-switch v-model="isVerifyList"> </el-switch>
          </div>
        </div>
      </div>
      <div class="footer">
        <el-button class="footer-btn" type="primary" @click="commit">{{ $t('发起审查') }}</el-button>
      </div>
    </el-form>
    <TaskLoadingDialog ref="taskLoadingDialogRef" @success="onSuccess" />
  </div>
</template>
<style lang="scss" scoped>
.custom-label {
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.375rem;
  color: var(--minor-font);
  text-align: left;
}
:deep(.el-loading-mask) {
  inset: 1px;
}
:deep(.el-loading-spinner) {
  top: 10%;
  display: flex;
  align-items: start;
  justify-content: flex-start;
  .el-loading-text {
    color: var(--is-color-7d7b89);
  }
  .circular {
    display: inline;
    width: 1.5rem;
    height: 1.5rem;
    margin-right: 1rem;
    animation: loading-rotate 2s linear infinite;
  }
}
.review-purpose {
  display: flex;
  flex: 1;
  align-items: center;
}
.purpose-list {
  display: flex;
  justify-content: space-between;
  width: 100%;
}
.purpose {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 0.5rem;
}
.i-icon {
  margin-left: 0.5rem;
  color: #bdbdbd;
  cursor: pointer;
}
.title {
  font-size: 1rem;
  font-weight: 500;
  color: var(--minor-font);
}
.sub-title {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: var(--is-color-7d7b89);
}
.i-icon-normal {
  color: #c0c4cc;
}
.i-active {
  color: var(--is-color-773bef);
}
.participant {
  display: flex;
  flex-direction: column;
}
.participant.active {
  .title {
    color: var(--is-color-773bef);
  }
  .sub-title {
    color: var(--is-color-773bef);
  }
}
.review-set {
  margin: 1rem 0.5rem;
  .set-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
.review-participant {
  :deep(.el-loading-mask) {
    top: 0;
    left: 0;
    background-color: var(--bg-color);
  }
  :deep(.el-loading-spinner) {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    line-height: 2.1875rem;
    text-align: center;
    .el-icon-loading {
      margin-right: 1rem;
      font-size: 1rem;
    }
    .el-loading-text {
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--is-color-7d7b89);
    }
  }
}
.error-message {
  padding-top: 0.25rem;
  font-size: 0.75rem;
  line-height: 1;
  color: #f56c6c;
}
.footer {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 50px;
  padding: 0 1.375rem 1rem 1rem;
  background-color: var(--bg-color);
  &-btn {
    width: 100%;
  }
}
.task-loading {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 2000;
  width: 32vw;
  min-width: 25.75rem;
  max-width: 36.25rem;
  height: 100vh;
  padding: 1rem;
  background-color: var(--bg-color);
}
.radio-circle {
  width: 1rem;
  height: 1rem;
  cursor: pointer;
  border: 1px solid #c0c4cc;
  border-radius: 50%;
}
</style>
