import { get, post, download } from '@/services'
import type { IResponse } from '@/services'
import type { AxiosResponse } from 'axios'

interface IGetUserByOrgIdParams {
  orgId: string
  subFlag?: boolean
  searchContent?: string
  page: number
  pageSize: number
}

// 类型定义
interface IAddOrgParams {
  orgName: string
  rootFlag: number
  parentId: number
}

interface IEditOrgParams {
  orgId: number
  orgName: string
  parentId: number
}

interface IDeleteOrgParams {
  orgIdList: string[]
}

interface IRoleItem {
  id: string
  roleName: string
  roleCode: string
  roleRemark: string | null
  updateTime: string
  createBy: string
  menuCodeList: any[] | null
  menuNameList: any[] | null
}

interface IOrgListItem {
  id: string
  deleteFlag: any
  createBy: string
  createTime: string
  updateTime: string
  updateBy: string | null
  versionNumber: any
  orgName: string
  parentId: string
  rootFlag: boolean
  orgOrder: string
  orgIdPath: string
  orgNamePath: string
  leaderUserCode: string | null
}

interface IUserListItem {
  companyName: string
  orgId: string
  orgName: string
  userCode: string
  realName: string | null
  userName: string
  userStatus: number
  roleList: IRoleItem[]
  roleCodeListString: string
  roleNameListString: string
  roleCodeList: string[]
  orgList: IOrgListItem[]
  orgIdListString: string
  orgNameListString: string
  orgUserOrder: string
  corpId: string | null
  corpName: string | null
  mainDepartment: string
  mainDepartmentName: string
  userId: string
  deleteFlag: any
  createBy: string | null
  createTime: string
  updateTime: string
  updateBy: string | null
}

interface IUserByOrgIdResponse {
  pageNum: number
  pageSize: number
  total: string
  totalPages: string
  list: IUserListItem[]
}

interface IQueryOrgPageListParams {
  searchContent?: string
  page?: number
  pageSize?: number
}

interface IAddUserParams {
  [key: string]: any
}

interface IEditUserParams {
  [key: string]: any
}

interface IBatchImportUserParams {
  [key: string]: any
}

interface IDeleteUserParams {
  userIdList: string[]
}
interface ICheckUserNameParams {
  userName: string
}
/**
 * 新增组织
 * @param { *orgName *rootFlag *parentId }
 * @returns
 */
export function addOrgRequest<T>(data: IAddOrgParams): Promise<IResponse<T>> {
  return post<T>(`/org/save-org`, data)
}

/**
 * 编辑组织
 * @param { *orgId *orgName *parentId }
 * @returns
 */
export function editOrgRequest<T>(data: IEditOrgParams): Promise<IResponse<T>> {
  return post<T>(`/org/update-org`, data)
}

/**
 * 删除组织
 * @param { *orgIdList[array] }
 * @returns
 */
export function deleteOrgRequest<T>(data: IDeleteOrgParams): Promise<IResponse<T>> {
  return post<T>(`/org/delete-org`, data)
}

/**
 *  通过组织id获取用户
 *  @param { *orgId:组织id, subFlag:是否查询子级, searchContent:搜索内容 }
 */
export function getUserByOrgId(data: IGetUserByOrgIdParams): Promise<IResponse<IUserByOrgIdResponse>> {
  return post<IUserByOrgIdResponse>(`/org/get-user-list-by-orgId`, data)
}

/**
 *  分页获取组织信息
 *  @param { searchContent, page, pageSize }
 */
export function queryOrgPageList<T>(data: IQueryOrgPageListParams): Promise<IResponse<T>> {
  return post<T>(`/org/get-org-list`, data)
}

// 新增用户
export function addUserRequest<T>(data: IAddUserParams): Promise<IResponse<T>> {
  return post<T>(`/org/user/save-user`, data)
}

// 编辑用户
export function editUserRequest<T>(data: IEditUserParams): Promise<IResponse<T>> {
  return post<T>(`/org/user/update-user`, data)
}

// 批量导入用户
export interface IBatchImportUserResult {
  cacheResultKey: string
  successTotal: number
  successList: Array<{
    realName: string
    userName: string
    userRole: string
    orgNameListString: string
    roleCode: string[]
    orgIdList: string[]
    order: number
    userCode: string
  }>
  errorTotal: number
  errorList: any[]
}

export function batchImportUser(data: IBatchImportUserParams): Promise<IResponse<IBatchImportUserResult>> {
  return post<IBatchImportUserResult>(`/org/user/import-batch-user`, data)
}

// 下载用户导入模板
export function templateDown<T>(): Promise<AxiosResponse<T>> {
  return download(`/合同产品-用户导入模板.xlsx`, {}, 'get')
}

/**
 * 下载错误信息
 * @param {*} checkKey
 * @returns
 */
export function downloadErrorInfo<T>(data: { checkKey: string }): Promise<AxiosResponse<T>> {
  return download(`/org/user/export-org-user-error?checkKey=${data.checkKey}`, {}, 'get')
}

// 删除用户
export function deleteUserRequest<T>(data: IDeleteUserParams): Promise<IResponse<T>> {
  return post<T>(`/org/user/delete-user`, data)
}

// 检查用户名
export function checkUserName<T>(data: ICheckUserNameParams): Promise<IResponse<T>> {
  return post<T>(`/user/checkUserName`, data)
}
