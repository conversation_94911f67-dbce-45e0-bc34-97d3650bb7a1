<template>
  <el-dialog
    :title="dialogTitle"
    v-model="visible"
    :close-on-click-modal="false"
    width="480px"
    :show-close="true"
    @close="closeDialog"
  >
    <div class="dialog-content-wrapper">
      <step-box ref="step" v-show="showType == 1" @upload="uploadCallback"></step-box>
      <upload-progress
        :textType="progressTextType"
        :value="progressNum"
        :status="progressStatus"
        v-show="showType == 2"
        @download="downloadErrorTemplate"
        @upload="uploadCallback"
      ></upload-progress>
      <input type="file" :accept="acceptList.join(',')" ref="upload" style="display: none" @change="uploadTemplate" />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click.stop="closeDialog">{{ $t('取消') }}</el-button>
        <el-button type="primary" @click.stop="submit">{{ $t('确定') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { batchImportUser, downloadErrorInfo } from '@/services/manage/index'
import stepBox from './stepBox.vue'
import uploadProgress from './uploadProgress.vue'
// import { useDownloadFile } from '@/composables/useDownloadFile'
import { $t } from '@/utils/i18n'
import { RESPONSE_CODE_SUCCESS } from '@/constants'
import { exportStream } from '@/utils'
import { postUpload } from '@/services/contract'
// const { downloadFile } = useDownloadFile()

const emit = defineEmits<{
  refresh: []
}>()

const dialogTitle = ref($t('批量导入组织'))
const visible = ref(false)
const acceptList = ref(['.xls', '.xlsx'])
const showType = ref(1)
const progressNum = ref(0)
const progressStatus = ref(0)
const progressTextType = ref(1)
const downloadErrorKey = ref<string | null>(null)
const loading = ref(false)
const timer = ref<NodeJS.Timeout | null>(null)

// Refs
const step = ref()
const upload = ref<HTMLInputElement>()

const refresh = () => {
  if (timer.value) clearTimeout(timer.value)

  timer.value = setTimeout(() => {
    emit('refresh')
  }, 200)
}

const closeDialog = () => {
  if (loading.value) return ElMessage.info($t('正在导入，请等待'))

  visible.value = false

  refresh()

  progressNum.value = 0
  showType.value = 1
  progressStatus.value = 0
  progressTextType.value = 1
  downloadErrorKey.value = null
  clearFiles()
}

const openDialog = () => {
  visible.value = true
}

const clearFiles = () => {
  if (upload.value) upload.value.value = ''
}

const uploadCallback = () => {
  upload.value?.click()
}

const uploadTemplate = async (e: Event) => {
  const target = e.target as HTMLInputElement
  const file = target.files?.[0]
  if (!file) return
  const formdata = new FormData()
  formdata.append('file', file)
  progressStatus.value = 1
  loading.value = true
  showType.value = 2
  progressNum.value = 0

  try {
    const uploadRes = await postUpload(formdata, {}, uploadProgressHandle)
    if (uploadRes.code === RESPONSE_CODE_SUCCESS) {
      const uploadData = uploadRes.data
      const res = await batchImportUser({ fileCode: uploadData[0].templateUrl })
      if (res.code !== RESPONSE_CODE_SUCCESS) {
        progressStatus.value = 4
        return
      }
      const importData = res.data
      if (importData.errorTotal > 0) {
        progressStatus.value = 3
        downloadErrorKey.value = importData.cacheResultKey
      } else {
        progressStatus.value = 2
      }
    }
  } catch (error) {
    progressStatus.value = 4
  }

  loading.value = false
  clearFiles()
}

const uploadProgressHandle = (num: number) => {
  progressNum.value = Math.ceil(num)
  if (progressNum.value >= 100) progressTextType.value = 2
}

const downloadErrorTemplate = async () => {
  const res = await downloadErrorInfo<Blob>({ checkKey: downloadErrorKey.value || '' })
  console.log(res, 'check res')
  exportStream(res, '错误数据.xlsx')
}

const submit = () => {
  closeDialog()
}

defineExpose({
  openDialog,
})
</script>
<style lang="scss" scoped>
.dialog-content-wrapper {
  min-height: 200px;
}
</style>
