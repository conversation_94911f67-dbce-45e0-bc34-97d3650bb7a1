import Layout from '@/layout/index.vue'
import type { RouteRecordRaw } from 'vue-router'

import { $t } from '@/utils/i18n'
const router: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/contract',
  },
  {
    path: '/sso',
    name: 'qyAuth',
    meta: {
      title: 'eui登录',
    },
    component: () => import(/* webpackChunkName: "qyAuth" */ '@/views/qyAuth/index.vue'),
  },
  {
    path: '/404',
    name: 'NoFound',
    components: {
      default: () => import(/* webpackChunkName: "404" */ '@/views/404.vue'),
    },
    meta: {
      title: $t('案例'),
    },
  },
  { path: '/:w+', name: '*', redirect: '/404' },
]
export default router
