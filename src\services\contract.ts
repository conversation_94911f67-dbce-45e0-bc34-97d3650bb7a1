import { post, get, put, del, upload, download, source } from '@/services'
import type { IResponse } from '@/services'

export function queryRecentContracts<T>(): Promise<IResponse<T>> {
  return post<T>(`/contract/recent-list`)
}

export function queryContractList<T>(params: any): Promise<IResponse<T>> {
  return get<T>(`/llm/llm-contract/page`, params)
}
export function getReivewRuleList<T>(): Promise<IResponse<T>> {
  //审查清单
  return get<T>(`/llm/get-rule-type-position-list`)
}
export function getRulesList<T>(ruleCode: string): Promise<IResponse<T>> {
  //审查规则
  return get<T>(`/llm/llm-review-rule-tree`, { ruleCode })
}
export function getCustomRulesList<T>(ruleCode: string): Promise<IResponse<T>> {
  return post<T>(`/llm-risk-rule-custom/list`, { ruleCode })
}
export function getAiReviewRuleType<T>(fileCode: string): Promise<IResponse<T>> {
  return post<T>(`/llm/llm-get-contract-type-and-participants`, { fileCode })
}
export function getAiReviewRulePurposeList<T>(
  contractType: string,
  fileCode: string,
  fileUrl: string,
  reviewPosition: string,
): Promise<IResponse<T>> {
  return post<T>(`/llm/llm-get-review-purpose`, { contractType, fileCode, fileUrl, reviewPosition })
}

/**
 *  根据合同ID获取审查记录
 * @param contractId
 * @returns
 */
export function getReviewInfoByContractId<T>(contractId: string): Promise<IResponse<T>> {
  return get<T>(`/llm/llm-review-record/review-record-by-contract/${contractId}`)
}

/**
 * 最新的合同列表
 * @returns
 */
export function getRecentContractList<T>(): Promise<IResponse<T>> {
  return get<T>(`/llm/llm-contract/recent-list`)
}

// 设置 userCode 获取是否开启 AI 问答
export function setAiEnable<T>(enableFlag: string): Promise<IResponse<T>> {
  return post<T>(`/user/set-ai-question-and-answering-enable-by-userCode?enableFlag=${enableFlag}`, { enableFlag })
}

/**
 * 查询自定义规则
 * @param id
 * @returns
 */
export function queryCustomRuleById<T>(id: string): Promise<IResponse<T>> {
  return get<T>(`/llm-risk-rule-custom/${id}`)
}
/**
 * 新增自定义规则
 * @param riskJudgePrompts
 * @param ruleCode
 * @param ruleName
 * @returns
 */
export function createCustomRule<T>(
  ruleCode: string,
  ruleName: string,
  riskJudgePrompts: string,
): Promise<IResponse<T>> {
  return post<T>(`/llm-risk-rule-custom`, { ruleCode, ruleName, riskJudgePrompts })
}
/**
 * 修改自定义规则
 * @param id
 * @param riskJudgePrompts
 * @param ruleCode
 * @param ruleName
 * @returns
 */
export function editCustomRule<T>(
  ruleCustomId: string,
  ruleName: string,
  riskJudgePrompts: string,
): Promise<IResponse<T>> {
  return put<T>('/llm-risk-rule-custom', {
    ruleCustomId: ruleCustomId,
    ruleName: ruleName,
    riskJudgePrompts: riskJudgePrompts,
  })
}
/**
 * 删除自定义规则
 * @param id
 * @returns
 */
export function delCustomRule<T>(ruleCustomId: string): Promise<IResponse<T>> {
  return del<T>('/llm-risk-rule-custom', { ruleCustomId })
}

/**
 * 获取匹配规则
 * @param fileCode
 * @returns
 */
export function getMatchRule<T>(fileCode: string): Promise<IResponse<T>> {
  return post<T>(`/llm/ai-get-rule-type-name`, { fileCode })
}

/**
 * 获取智能审核配置
 * @param contractName
 * @param contractType
 * @returns
 */
export function getReviewRuleConfig<T>(
  contractName: string,
  contractType: string,
  fileCode: string,
  reviewPosition: string,
  reviewPurposeList: string[],
  fileUrl = '',
): Promise<IResponse<T>> {
  return post<T>(`/llm/llm-get-review-rule-List`, {
    contractName,
    contractType,
    fileCode,
    reviewPosition,
    reviewPurposeList,
    fileUrl,
  })
}

/**
 * 草稿生成
 * @param data
 * @returns
 */
export function draftGenerate<T>(data: any): Promise<IResponse<T>> {
  return post<T>(`/llm/llm-contract/generate`, data)
}
/**
 * 上传文件
 * @param data
 * @returns
 */
export interface IUploadTemplateResult {
  templateName: string
  templateUrl: string
}

export function postUpload(
  data: any,
  config: any,
  fn?: (progress: number) => void,
): Promise<IResponse<IUploadTemplateResult[]>> {
  return upload<IUploadTemplateResult[]>(
    '/attachment/upload-temporary',
    data,
    { cancelToken: source.token, ...config },
    fn,
  )
}

// 获取当前审查清单的解析状态
export function getAiProgress<T>(ruleListId: string): Promise<IResponse<T>> {
  return get<T>(`/llm/llm-review-rule-list/get-analysis-progress-by-rule-list-id`, { ruleListId })
}
/**
 * 获取合同审查结果
 * @param data
 * @returns
 */
export function contractReview<T>(data: any): Promise<IResponse<T>> {
  return post<T>('/llm/contract-review', data)
}

/**
 * 创建合同
 * @param data
 * @returns
 */
export function createContact<T>(data: any): Promise<IResponse<T>> {
  return post<T>(`/llm/llm-contract/create`, data)
}

/**
 * 删除合同
 * @param contractId
 * @returns
 */
export function delContract<T>(contractId: string): Promise<IResponse<T>> {
  return del<T>(`/llm/llm-contract/remove/${contractId}`)
}

// 下载合同审查意见书
export function reviewRecordDownload<T>(data: any): Promise<IResponse<T>> {
  return download<T>(`/llm/llm-review-record/download-review-comment`, data)
}

/**
 * 生成合同摘要
 * @param fileCode
 * @returns
 */
export function generateContratSummary<T>(fileCode: string): Promise<IResponse<T>> {
  return post<T>(`/llm/contract-summary/generate-contract-summary`, { fileCode })
}

export function getAllRuleList<T>(ruleListId: string): Promise<IResponse<T>> {
  return get<T>(`/llm/llm-review-rule-list/list-all-rule-by-rule-id?ruleListId=${ruleListId}`)
}

export function queryReviewRuleIterm<T>(data: any): Promise<IResponse<T>> {
  return post<T>('/llm/llm-get-review-rule-iterm', data)
}

export function queryRiskLabelName<T>(data: any): Promise<IResponse<T>> {
  return post<T>('/llm/llm-get-risk-label-name', data)
}
export function saveReviewRule<T>(data: any): Promise<IResponse<T>> {
  return post<T>(`llm/save-all-rule-list-at-once`, data)
}
