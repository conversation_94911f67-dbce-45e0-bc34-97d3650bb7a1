import axios from 'axios'
import { BASE_API } from '@/utils/config'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores'
import type { AxiosResponse } from 'axios'

import { $t } from '@/utils/i18n'
export type IResponse<T> = {
  code: string
  data: T
  message: string
  success: boolean
}

// create an axios instance
const service = axios.create({
  baseURL: BASE_API,
  timeout: 30000000, // request timeout
})

// request interceptor
service.interceptors.request.use(
  (config) => {
    const appStore = useAppStore()
    const userStore = useUserStore()
    const token = userStore.getToken()
    if (token) {
      config.headers['csrf_token'] = token
    }

    config.cancelToken = new axios.CancelToken((cancel) => {
      appStore.addAxiosCancelToken({
        cancel,
        url: config.url || '',
      })
    })

    if (config.method === 'get') {
      if (!config.params) {
        config.params = { _t: +new Date() }
      } else {
        config.params._t = +new Date()
      }
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

// response interceptor
service.interceptors.response.use(
  (response) => {
    if (response.data instanceof Blob) return response
    const res = response.data

    if (res instanceof ArrayBuffer) return res
    if (res.success === true || res.code == 200 || res.code === '000000') return res

    errorHandle(+res.code, res.message)

    if ([401, 403].includes(+res.code)) {
      return reLogin()
    }
    return Promise.reject(res.message || res.msg || 'Error')
  },
  (error) => {
    if (error.message && error.message === $t('路由跳转取消请求'))
      return new Promise(() => {
        console.log('中断')
      })
    if (error.response && error.response.data) {
      error.message = error.response.data.msg || error.response.data.error || $t('服务器内部错误')
    } else if (error.message && error.message === 'Request failed with status code 405') {
      error.message = $t('无权限访问')
    }
    if ([401, 403].includes(+error.response.status)) {
      return reLogin()
    }
    if (error.response.data instanceof Blob) {
      const reader = new FileReader()
      reader.onload = function (event) {
        const result = event.target?.result
        let obj = { message: $t('服务器内部错误') }
        try {
          obj = JSON.parse(result as string)
        } catch (error) {
        } finally {
          ElMessage({
            message: obj.message,
            type: 'error',
            duration: 5 * 1000,
          })
          return Promise.reject(obj.message)
        }
      }
      reader.readAsText(error.response.data)
    } else {
      ElMessage({
        message: error.message,
        type: 'error',
        duration: 5 * 1000,
      })
      return Promise.reject(error.message || 'Error')
    }
  },
)

function reLogin() {
  const userStore = useUserStore()
  ElMessage({
    message: $t('您的会话已过期，请重新登录'),
    type: 'error',
    duration: 2000,
  })
  userStore.initUserInfo()
  userStore.setShowLoginBox(true)
}

/**
 * 请求失败后的错误统一处理
 * @param {Number} status 请求失败的状态码
 */
const errorHandle = (status: number, other?: string) => {
  let errorInfo = ''
  // 状态码判断
  switch (status) {
    case 401:
      errorInfo = $t('您的会话已过期，请重新登录')
      break
    case 403:
      errorInfo = $t('您的权限受到限制，请咨询管理员获取权限')
      break
    case 404:
      errorInfo = $t('错误的参数或请求地址，请检查')
      break
    case 411:
      errorInfo = $t('您的会话已过期，请重新登录')
      break
    case 500:
      errorInfo = other || $t('服务器内部错误')
      break
    default:
      errorInfo = other || $t('网络异常，请稍后再试')
  }
  ElMessage({
    message: errorInfo,
    type: 'error',
    duration: 5 * 1000,
  })
}

const CancelToken = axios.CancelToken
export const source = CancelToken.source()
export default service

const { request } = service

export const post = <T>(url: string, data: any = {}, config?: any): Promise<IResponse<T>> =>
  request({
    url,
    method: 'post',
    data,
    ...config,
  })

export const get = <T>(url: string, params: any = {}, config?: any): Promise<IResponse<T>> =>
  request({
    url,
    method: 'get',
    params,
    ...config,
  })
export const put = <T>(url: string, data: any = {}): Promise<IResponse<T>> =>
  request({
    url,
    method: 'put',
    data,
  })
export const del = <T>(url: string, data: any = {}): Promise<IResponse<T>> =>
  request({
    url,
    method: 'DELETE',
    data,
  })

export const upload = <T>(
  url: string,
  data?: any,
  config?: any,
  onUploadProgress?: (progress: number) => void,
): Promise<IResponse<T>> =>
  request({
    url,
    method: 'post',
    data,
    onUploadProgress: onUploadProgress
      ? function (progressEvent: any) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onUploadProgress(progress)
      }
      : undefined,
    ...config,
  })

export const download = <T>(url: string, data: any = {}, method?: 'post' | 'get'): Promise<AxiosResponse<T>> => {
  console.log('下载文件:', url, data)
  return request({
    url,
    method: method || 'post',
    data,
    responseType: 'blob', // 设置响应类型为 blob
  })
}
