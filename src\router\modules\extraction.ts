import Layout from '@/layout/index.vue'
import type { RouteRecordRaw } from 'vue-router'

const router: RouteRecordRaw[] = [
  {
    path: '/extraction',
    component: Layout,
    children: [
      {
        path: '',
        name: 'Extraction',
        meta: {
          title: '信息抽取',
          permission: 'ContractExtract',
          custom: true,
        },
        component: () => import(/* webpackChunkName: "Extraction" */ '@/views/extraction/index.vue'),
      },
    ],
  },
]
export default router
