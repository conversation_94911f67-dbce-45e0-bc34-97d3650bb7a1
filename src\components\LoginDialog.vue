<script lang="ts" setup>
import { useAccountService } from '@/composables/useAccountService'
import { useUserStore } from '@/stores'
import { isLocal } from '@/utils/config'

const loginType = ref('qrcode')
const userStore = useUserStore()
const {
  getLoginCode,
  doLoginByCode,
  isAgreed,
  codeBtnName,
  userName,
  account,
  password,
  verificationCode,
  showAgreement,
  clearLoginForm,
  doLoginByPwd,
} = useAccountService()

const isRefreshing = ref(false)

const refreshCode = async () => {
  isRefreshing.value = true
  await userStore.getQrCode()
  await userStore.startScan()
  isRefreshing.value = false
}

const closeLoginForm = () => {
  userStore.showLoginBox = false
  loginType.value = 'qrcode'
  userStore.stopScan()
  clearLoginForm()
}

const agreeAgreement = () => {
  isAgreed.value = true
  showAgreement.value = false
}
const disagreeAgreement = () => {
  isAgreed.value = false
  showAgreement.value = false
}
const loginDialogVisible = computed(() => {
  return userStore.showLoginBox
})

const setLoginType = (type: string) => {
  loginType.value = type
  if (type === 'qrcode') {
    userStore.getQrCode()
    userStore.startScan()
  } else {
    userStore.stopScan()
  }
}

const scanwatch = watch(
  () => loginDialogVisible.value,
  (flag) => {
    if (flag && !isLocal) {
      userStore.getQrCode()
      userStore.startScan()
    } else {
      userStore.stopScan()
    }
  },
  {
    immediate: true,
    deep: true,
  },
)

onUnmounted(() => {
  scanwatch()
})
</script>

<template>
  <div>
    <el-dialog
      v-model="loginDialogVisible"
      width="43.75rem"
      append-to-body
      :close-on-click-modal="false"
      header-class="login"
      @close="closeLoginForm"
    >
      <div class="login-dialog-container">
        <div class="login-dialog-content-left">
          <img src="../assets/images/iterms-logo.png" alt="" class="iterms-logo" />
          <div class="left-img-container">
            <img
              src="https://cdn.fadada.com/dist/static/c/39/20250731095519_821eca08-926d-4050-be09-8bb9aa943218.png"
              class="left-img"
            />
          </div>
        </div>

        <div class="login-dialog-content-right">
          <div v-if="isLocal" class="login-box">
            <div class="login-tabs">
              <div class="tab">{{ $t('账号登录') }}</div>
            </div>

            <div class="verification-login-container verification-pwd-login-container">
              <div class="input-container">
                <div class="input-content">
                  <el-input v-model="account" :placeholder="$t('请输入账号')" clearable> </el-input>
                </div>
                <div class="input-content">
                  <el-input v-model="password" :placeholder="$t('请输入密码')" type="password" show-password clearable>
                  </el-input>
                </div>
              </div>

              <div class="login-button-container">
                <el-button type="primary" class="login-btn" @click="doLoginByPwd">{{ $t('登 录 / 注 册') }}</el-button>
              </div>
              <div class="agreement-container">
                <el-checkbox type="checkbox" v-model="isAgreed" class="agreement-checkbox" />
                <span>{{ $t('已阅读并同意') }}</span>
                <a href="https://legal.fadada.com/policy/1920378444479348736" target="_blank" class="agreement-link"
                  >《{{ $t('服务协议') }}》</a
                >
                <span>{{ $t('和') }}</span>
                <a href="https://legal.fadada.com/policy/1920377982875222016" target="_blank" class="agreement-link"
                  >《{{ $t('隐私政策') }}》</a
                >
              </div>
            </div>
          </div>
          <div v-else class="login-box">
            <div class="login-tabs">
              <div class="tab" :class="loginType === 'qrcode' ? 'act' : ''" @click="setLoginType('qrcode')">
                {{ $t('扫码登录') }}
              </div>
              <div class="tab" :class="loginType === 'code' ? 'act' : ''" @click="setLoginType('code')">
                {{ $t('验证码登录') }}
              </div>
            </div>

            <div v-if="loginType === 'code'" class="verification-login-container verification-code-login-container">
              <div class="input-container">
                <div class="input-content">
                  <el-input v-model="userName" :placeholder="$t('请输入手机号')" maxlength="11" type="tel">
                    <template #prepend> <span>+ 86</span> </template>
                  </el-input>
                </div>
                <div class="input-content">
                  <el-input
                    v-model="verificationCode"
                    :placeholder="$t('请输入验证码')"
                    :clearable="false"
                    maxlength="6"
                  >
                    <template #append>
                      <span class="send_code_btn" @click="getLoginCode">{{ codeBtnName }}</span>
                    </template>
                  </el-input>
                </div>
              </div>

              <div class="login-button-container">
                <el-button type="primary" class="login-btn" @click="doLoginByCode">{{ $t('登 录 / 注 册') }}</el-button>
              </div>
              <div class="agreement-container">
                <el-checkbox type="checkbox" v-model="isAgreed" class="agreement-checkbox" />
                <span>{{ $t('已阅读并同意') }}</span>
                <a href="https://legal.fadada.com/policy/1920378444479348736" target="_blank" class="agreement-link"
                  >《{{ $t('服务协议') }}》</a
                >
                <span>{{ $t('和') }}</span>
                <a href="https://legal.fadada.com/policy/1920377982875222016" target="_blank" class="agreement-link"
                  >《{{ $t('隐私政策') }}》</a
                >
              </div>
            </div>

            <div v-else class="qrcode-container">
              <div class="qrcode-header">{{ $t('微信扫码登录') }}</div>
              <div class="qrcode-content">
                <div class="qrcode-box">
                  <div v-if="userStore.isQrCodeExpired" class="overlay"></div>
                  <div v-if="userStore.isQrCodeExpired" class="expried-tip">
                    <div>
                      <div @click="refreshCode" style="cursor: pointer" :class="!isRefreshing ? '' : 'refresh-active'">
                        <i class="iconfont" style="font-size: 2.25rem"> &#xe66d;</i>
                      </div>
                    </div>
                  </div>
                  <img v-show="userStore.qrCode" class="qrcode" :src="userStore.qrCode" />
                </div>
              </div>
              <div class="agreement-container">
                <span>{{ $t('扫码默认阅读并同意') }}</span>
                <a href="https://legal.fadada.com/policy/1920378444479348736" target="_blank" class="agreement-link"
                  >《{{ $t('服务协议') }}》</a
                >
                <span>{{ $t('和') }}</span>
                <a href="https://legal.fadada.com/policy/1920377982875222016" target="_blank" class="agreement-link"
                  >《{{ $t('隐私政策') }}》</a
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <el-dialog v-model="showAgreement" width="480px" @close="showAgreement = false">
      <template #header>
        <div class="agreement-dialog-header-title">{{ $t('服务协议和隐私保护') }}</div>
      </template>
      <div class="agreement-dialog-desc">{{ $t('我已阅读并同意 用户协议 隐私协议 和产品服务协议') }}</div>
      <template #footer>
        <div class="agreement-dialog-footer">
          <el-button size="small" @click="disagreeAgreement">{{ $t('不同意') }}</el-button>
          <el-button type="primary" size="small" @click="agreeAgreement">{{ $t('同意') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-input__wrapper.is-focus) {
  .el-input__icon {
    color: var(--minor-font);
  }
}
:deep(.el-dialog__body) {
  margin-top: 1rem;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.refresh-active {
  animation: rotate 1s linear infinite;
}
.login-dialog-container {
  display: flex;
  height: 20.5rem;
  .login-dialog-content-left {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 17.75rem;
    margin: 0 auto;
    .iterms-logo {
      position: absolute;
      top: 1.5rem;
      left: 1.5rem;
      z-index: 1;
      height: 2rem;
    }
    .left-img-container {
      position: absolute;
      top: 0;
      left: 0;
      .left-img {
        width: 18.75rem;
        border-top-left-radius: 0.5rem;
        border-bottom-left-radius: 0.5rem;
      }
    }
  }
  .login-dialog-content-right {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    width: 24rem;
    margin-left: 1rem;
    .login-box {
      width: 20rem;
    }
    .login-tabs {
      display: flex;
      gap: 2.5rem;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease-in-out;
      .tab {
        font-size: 1.125rem;
        font-weight: 400;
        line-height: 1.75rem;
        color: var(--font-color);
        cursor: pointer;
      }
      .act {
        font-weight: 600;
        color: var(--main-font);
        border-bottom: 2px solid var(--main-font);
      }
    }
    .verification-login-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      .input-container {
        width: 100%;
        margin-top: 2.25rem;
        .input-content {
          .send_code_btn {
            width: 5rem;
            font-size: 1rem;
            color: var(--iterms-band-main-color);
            cursor: pointer;
          }
          .send_code_btn.disabled {
            color: var(--iterms-text-gray-color);
            cursor: not-allowed;
          }
          :deep(.el-input) {
            width: 100%;
            height: 2.75rem;
            border: 1px solid var(--is-color-dcdfe6) !important;
            border-radius: 0.5rem !important;
          }
          :deep(.el-input-group__prepend) {
            background-color: var(--bg-color);
            border-radius: 0.5rem !important;
            box-shadow: none !important;
          }
          :deep(.el-input__wrapper) {
            border-left: none !important;
            border-radius: 0.5rem !important;
            box-shadow: none !important;
          }
          :deep(.el-input-group__append) {
            background-color: var(--bg-color);
            border-radius: 0.5rem !important;
            box-shadow: none !important;
          }
        }
        .input-content:last-of-type {
          margin-top: 1.5rem;
        }
      }
      .login-button-container {
        width: 100%;
        margin-top: 2rem;
        .login-btn {
          width: 100%;
          height: 3rem;
          font-size: 1.125rem;
          font-weight: 500;
          border-radius: 0.5rem;
        }
      }
      .agreement-container {
        display: flex;
        align-items: center;
        margin-top: 1rem;
        .agreement-checkbox {
          width: 1rem;
          height: 1rem;
          margin-right: 0.5rem;
          cursor: pointer;
        }
        .agreement-link {
          color: var(--main-font);
          text-decoration: none;
          cursor: pointer;
        }
      }
    }
    .qrcode-container {
      display: flex;
      flex: 1;
      flex-direction: column;
      margin-top: 24px;
      .qrcode-header {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        font-weight: 500;
      }
      .qrcode-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .qrcode-box {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 10rem;
          height: 10rem;
          .qrcode {
            width: 10rem;
            height: 10rem;
            margin-top: 1rem;
          }
          .overlay {
            position: absolute;
            width: 10rem;
            height: 10rem;
            margin-top: 1rem;
            background-color: var(--bg-color);
            border: 1px solid #ddd;
            border-radius: 4px;
            opacity: 0.95;
          }
          .expried-tip {
            position: absolute;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 10rem;
            height: 10rem;
            margin-top: 1rem;
          }
        }
      }
      .agreement-container {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 2rem;
        .agreement-link {
          color: var(--main-font);
          text-decoration: none;
          cursor: pointer;
        }
      }
    }
  }
}
.agreement-dialog-header-title {
  font-size: 1.125rem;
}
.agreement-dialog-desc {
  color: var(--is-color-7d7b89);
}
.agreement-dialog-footer {
  text-align: right;
}
</style>
