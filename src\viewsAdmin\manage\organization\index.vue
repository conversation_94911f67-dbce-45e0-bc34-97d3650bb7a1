<template>
  <div class="organization-page">
    <div class="page-content clearfix">
      <div class="left-container">
        <div class="search-container">
          <el-input
            v-model="matchOrgStr"
            clearable
            :maxlength="50"
            :placeholder="$t('搜索组织')"
            @keyup.enter="searchHandleOrg"
            @input="inputHandleOrg"
          >
            <template #suffix>
              <i class="el-input__icon el-icon-search search_icon" @click.stop="searchHandleOrg"></i>
            </template>
          </el-input>
        </div>
        <div class="tree-container">
          <el-scrollbar v-loading="orgLoading" wrap-class="scrollbar-wrapper">
            <div
              v-if="!orgList.length && !isInit && $nodeAuth(['270202'])"
              class="organ-add-btn"
              @click.stop="addOrganization(rootNode, 1)"
            >
              <span class="el-icon-plus btn-icon"></span>
              <span>{{ $t('新建组织') }}</span>
            </div>
            <div class="organ-tree">
              <el-tree
                v-show="orgList.length"
                ref="tree"
                node-key="id"
                :props="defaultProps"
                highlight-current
                :default-expanded-keys="expandedKeys"
                :filter-node-method="filterOrgNode"
                :data="orgList"
              >
                <template #default="{ node, data }">
                  <div class="el-tree-node__label" @click.stop="orgClick(data, node)">
                    <div class="edit-input">
                      <div class="label-info" :class="{ primary: currentOrgId == data.id }">{{ data.orgName }}</div>
                    </div>
                    <div class="node-action" @click.stop.prevent>
                      <el-button v-if="$nodeAuth(['270201'])" type="text">
                        <el-dropdown
                          v-if="node.level > 1"
                          :hide-on-click="false"
                          @command="commandHandle($event, data)"
                        >
                          <i class="iconfont" style="font-size: 1rem">&#xe642;</i>
                          <span class="btn el-icon-more"></span>
                          <template #dropdown>
                            <el-dropdown-menu>
                              <el-dropdown-item :command="'resetOrgName'">{{ $t('重命名') }}</el-dropdown-item>
                              <!-- <el-dropdown-item v-if="!data.leaderUserCode" command="setLeaderForOrg">{{
                                $t('设置负责人')
                              }}</el-dropdown-item>
                              <el-dropdown-item v-else command="delLeaderForOrg">{{
                                $t('取消负责人')
                              }}</el-dropdown-item> -->
                              <el-dropdown-item v-if="$nodeAuth(['270203'])" :command="'deleteOrg'">{{
                                $t('删除')
                              }}</el-dropdown-item>
                            </el-dropdown-menu>
                          </template>
                        </el-dropdown>
                      </el-button>
                      <el-button
                        v-if="$nodeAuth(['270202'])"
                        type="text"
                        class="btn"
                        @click.stop="addOrganization(data, 0)"
                      >
                        <i class="iconfont" style="font-size: 1rem">&#xe66a;</i>
                      </el-button>
                    </div>
                  </div>
                </template>
              </el-tree>
            </div>
          </el-scrollbar>
        </div>
      </div>
      <div class="right-container">
        <div class="action-box">
          <div class="right-side-btn">
            <el-button
              v-if="isLocal && $nodeAuth(['270205'])"
              plain
              type="primary"
              class="custom-primary-hover"
              @click.stop="importHandle"
              >{{ $t('批量导入') }}</el-button
            >
            <el-button v-if="$nodeAuth(['270204'])" type="primary" @click.stop="addNewUser">{{
              $t('新增用户')
            }}</el-button>
            <!-- <el-button v-if="$nodeAuth(['270206'])" :disabled="selectUserList.length == 0" @click.stop="changeDep">{{
              $t('移动人员')
            }}</el-button> -->
          </div>

          <div class="user-search">
            <el-input
              v-model="matchUserStr"
              clearable
              :maxlength="50"
              :placeholder="$t('搜索用户')"
              @keyup.enter="searchHandleUser"
              @input="inputHandleUser"
            >
              <template #suffix>
                <i class="el-input__icon el-icon-search search_icon" @click.stop="searchHandleUser"></i>
              </template>
            </el-input>
          </div>
        </div>
        <div class="table-container">
          <el-table
            ref="table"
            v-loading="tableLoading"
            height="300px"
            :row-key="getTableKey"
            class="custom-table-class"
            :data="tableList"
            :header-cell-style="getRowClass"
            @selection-change="handleSelectionChange"
          >
            <el-table-column reserve-selection type="selection" width="50"></el-table-column>
            <el-table-column :label="$t('用户名称')" :show-overflow-tooltip="true">
              <template #default="scope">{{ scope.row.realName }}</template>
            </el-table-column>
            <el-table-column :label="$t('用户账户')" :show-overflow-tooltip="true">
              <template #default="scope">{{ scope.row.userName }}</template>
            </el-table-column>
            <el-table-column :label="$t('用户角色')" :show-overflow-tooltip="true">
              <template #default="scope">{{ scope.row.roleNameListString }}</template>
            </el-table-column>
            <el-table-column :label="$t('归属部门')" :show-overflow-tooltip="true">
              <template #default="scope">{{ scope.row.orgNameListString || '--' }}</template>
            </el-table-column>
            <el-table-column :label="$t('主部门')" :show-overflow-tooltip="true">
              <template #default="scope">{{ scope.row.mainDepartmentName || '--' }}</template>
            </el-table-column>
            <el-table-column :label="$t('操作')" width="200" fixed="right">
              <template #default="scope">
                <HandleBox :handle-data="scope.row" :handle-list="getHandleList()" />
              </template>
            </el-table-column>
          </el-table>

          <div class="foot-container">
            <el-pagination
              v-show="tableList.length >= 0"
              :current-page="currentPage"
              :page-sizes="[10, 20, 30, 40]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>

    <add-org ref="addOrgDialog" @succ="refreshOrgList"></add-org>
    <add-user ref="addUserDialog" @succ="refreshUserList"></add-user>

    <!-- 导入用户 -->
    <import-user ref="importUserDialog" @refresh="refreshPageInfo"></import-user>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  queryOrgPageList,
  deleteOrgRequest,
  getUserByOrgId,
  deleteUserRequest,
  // setLeaderReq,
  // delOrgLeader,
  queryPageRoleList,
} from '@/services/manage/index'
import type { IQueryPageRoleList } from '@/services/manage/index'
import { cloneObject } from '@/utils'
// import { userStatusList, superiorFlagList, workHandoverStatusList } from './type.ts'
import HandleBox from '@/components/handleBox/index.vue'
import AddOrg from './components/AddOrg.vue'
import AddUser from './components/AddUser.vue'
import importUser from './components/importUser/index.vue'
// import { useRouter } from 'vue-router'
import { useOrganizationStore } from '@/stores/index'
import { RESPONSE_CODE_SUCCESS } from '@/constants'
import { $t } from '@/utils/i18n'
import { isLocal } from '@/utils/config'

// 接口定义
// interface OrgNode {
//   id: number
//   orgId?: number
//   orgName: string
//   orgIdPath: string
//   pid?: number
//   rootFlag: boolean
//   orgOrder?: number
//   leaderUserCode?: string
//   leaderName?: string
//   subList?: OrgNode[]
// }

// API 返回的组织数据结构
interface OrgNode {
  id: string
  orgId: string | null
  orgName: string
  parentId: string
  rootFlag: boolean
  orgOrder: number
  orgIdPath: string
  orgNamePath: string
  hasChild: boolean | null
  leaderUserCode: string | null
  leaderName: string | null
  children: OrgNode[]
}

// API 响应类型
interface OrgListResponse {
  code: string
  data: OrgNode[]
  message?: string
}

interface IRoleItem {
  id: string
  roleName: string
  roleCode: string
  roleRemark: string | null
  updateTime: string
  createBy: string
  menuCodeList: any[] | null
  menuNameList: any[] | null
}

interface IOrgListItem {
  id: string
  deleteFlag: any
  createBy: string
  createTime: string
  updateTime: string
  updateBy: string | null
  versionNumber: any
  orgName: string
  parentId: string
  rootFlag: boolean
  orgOrder: string
  orgIdPath: string
  orgNamePath: string
  leaderUserCode: string | null
}

interface IUserListItem {
  companyName: string
  orgId: string
  orgName: string
  userCode: string
  realName: string | null
  userName: string
  userStatus: number
  roleList: IRoleItem[]
  roleCodeListString: string
  roleNameListString: string
  roleCodeList: string[]
  orgList: IOrgListItem[]
  orgIdListString: string
  orgNameListString: string
  orgUserOrder: string
  corpId: string | null
  corpName: string | null
  mainDepartment: string
  mainDepartmentName: string
  userId: string
  deleteFlag: any
  createBy: string | null
  createTime: string
  updateTime: string
  updateBy: string | null
}

// 响应式数据
const orgList = ref<OrgNode[]>([])
const originalTree = ref<OrgNode[]>([])
const matchOrgStr = ref<string | null>(null)
const matchUserStr = ref<string | null>(null)
const defaultProps = reactive({ children: 'children', label: 'orgName' })
const tableList = ref<IUserListItem[]>([])
const tableLoading = ref(false)
const orgLoading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const expandedKeys = ref<string[]>([])
const rootNode = reactive({
  orgId: 0,
  rootFlag: 1,
})
const isInit = ref(true)
const inputTimer = ref<NodeJS.Timeout | null>(null)
const inputUserTimer = ref<NodeJS.Timeout | null>(null)
const currentOrgId = ref<string | null>(null)
const currentOrgPath = ref<string | null>(null)
const curOrgInfo = ref<Partial<OrgNode>>({})
const selectUserList = ref<string[]>([])
const isCreate = ref(true)

// 模板引用
const tree = ref<any>()
const table = ref<any>()
const addOrgDialog = ref<any>()
const addUserDialog = ref<any>()
const importUserDialog = ref<any>()
// const changeDepartmentDialog = ref<any>()
// const setLeaderDialog = ref<any>()

// 路由和store
// const router = useRouter()
const organizationStore = useOrganizationStore()

// 全局属性
const { proxy } = getCurrentInstance() as any
const $nodeAuth = proxy.$nodeAuth

// 计算属性和方法
// 方法定义
const getHandleList = () => {
  const list = [
    { name: $t('编辑'), fun: editUser, auth: '270207' },
    { name: $t('删除'), fun: deleteUser, auth: '270208' },
  ]
  // if (!row.leaderFlag) {
  //   list.push({ name: $t('设为负责人'), fun: setLeaderOnUser, auth: '270207' })
  // } else {
  //   list.push({ name: $t('取消负责人'), fun: (row: any) => delLeaderOnUser(row, null), auth: '270207' })
  // }
  return list
}

// const transformValue = (val: any, list: string, type: string) => {
//   const listData =
//     list === 'userStatusList'
//       ? userStatusList
//       : list === 'superiorFlagList'
//       ? superiorFlagList
//       : list === 'workHandoverStatusList'
//       ? workHandoverStatusList
//       : []
//   const result = listData.filter((item) => item.value == val)

//   if (result.length) return (result[0] as any)[type]
//   else return type == 'className' ? 'defult' : '--'
// }

const refreshPageInfo = () => {
  refreshOrgList()
  refreshUserList()
}

// const initHandle = () => {
//   searchOrgList()
//   // // 组织部门下拉
//   // organizationStore.searchOrgSelect()
//   // // 角色下拉
//   // organizationStore.searchRoleSelect()
// }

const searchHandleOrg = () => {
  tree.value.filter(matchOrgStr.value)
}

const searchHandleUser = () => {
  filterUser()
}

const inputHandleUser = () => {
  if (inputUserTimer.value) clearTimeout(inputUserTimer.value)
  inputUserTimer.value = setTimeout(() => {
    filterUser()
  }, 500)
}

const inputHandleOrg = () => {
  if (inputTimer.value) clearTimeout(inputTimer.value)
  inputTimer.value = setTimeout(() => {
    tree.value.filter(matchOrgStr.value)
  }, 500)
}

const filterUser = () => {
  refreshUserList()
}

const filterOrgNode = (value: string, data: any): boolean => {
  if (!value) return true
  return data.orgName.indexOf(value) !== -1
}

const getRowClass = ({ rowIndex }: { rowIndex: number }) => {
  if (rowIndex === 0) {
    return {
      background: '#F7F8F9',
      borderTop: '1px solid #E8E8E8',
      color: '#262626',
      fontWeight: '500',
      fontSize: '12px',
    }
  } else {
    return {}
  }
}

const getTableKey = (row: IUserListItem): string => {
  return row.userCode
}

const expandHandle = (orgId: string): void => {
  expandedKeys.value.push(orgId)
}

const collapseHandle = (orgId: string): void => {
  expandedKeys.value = expandedKeys.value.filter((id) => id !== orgId)
}

const addOrganization = (node: any, rootFlag: number) => {
  const data = {
    orgName: '',
    parentId: node.orgId || node.id,
    rootFlag: rootFlag,
  }
  if (addOrgDialog.value && addOrgDialog.value.openDialog) {
    addOrgDialog.value.openDialog(data)
  }
}

const searchOrgList = async (): Promise<void> => {
  orgLoading.value = true
  try {
    const result = await queryOrgPageList<OrgListResponse>({})
    if (result.code === RESPONSE_CODE_SUCCESS) {
      const data = Array.isArray(result.data) ? result.data : []
      orgList.value = loopSortTree(data)
      organizationStore.setOrgList(orgList.value)
      originalTree.value = cloneObject(orgList.value || [])

      if (orgList.value.length && isCreate.value) {
        isCreate.value = false
        curOrgInfo.value = orgList.value[0]
        currentOrgId.value = orgList.value[0].id
        currentOrgPath.value = orgList.value[0].orgIdPath
        expandHandle(currentOrgId.value)
        refreshUserList()
      }
    }
  } catch (error) {
    ElMessage.error($t('获取组织列表失败，请稍后重试'))
  } finally {
    orgLoading.value = false
    if (isInit.value) isInit.value = false
  }
}

const handleSizeChange = (val: number): void => {
  currentPage.value = 1
  pageSize.value = val
  searchUserList()
}

const handleCurrentChange = (val: number): void => {
  currentPage.value = val
  searchUserList()
}

const searchUserList = async (): Promise<void> => {
  if (!currentOrgId.value) return
  const params = {
    searchContent: matchUserStr.value || '',
    orgId: currentOrgId.value,
    subFlag: true,
    page: currentPage.value,
    pageSize: pageSize.value,
  }
  tableLoading.value = true
  try {
    const { code, data } = await getUserByOrgId(params)
    if (code === RESPONSE_CODE_SUCCESS) {
      tableList.value = data.list || []
    }
    total.value = parseInt(data.total) || 0
  } catch (error) {
  } finally {
    tableLoading.value = false
  }
}

const loopSortTree = (list: OrgNode[]): OrgNode[] => {
  const sortedList = list
    .map((node) => ({ ...node }))
    .sort((a: OrgNode, b: OrgNode) => {
      return (a.orgOrder || 0) - (b.orgOrder || 0)
    })

  sortedList.forEach((node: OrgNode) => {
    if (node.children && node.children.length > 0) {
      node.children = loopSortTree(node.children)
    }
  })

  return sortedList
}

const refreshOrgList = () => {
  searchOrgList()
}

const refreshUserList = () => {
  table.value.clearSelection()
  currentPage.value = 1
  searchUserList()
}

const orgClick = (orgData: OrgNode, node: any): void => {
  node.expanded = !node.expanded
  if (node.expanded) expandHandle(orgData.id)
  else collapseHandle(orgData.id)
  curOrgInfo.value = orgData
  currentOrgPath.value = orgData.orgIdPath
  currentOrgId.value = orgData.id

  refreshUserList()
}

const commandHandle = (command: string, row: OrgNode): void => {
  const commands: Record<string, (param: any) => void | Promise<void | boolean>> = {
    resetOrgName,
    // setLeaderForOrg,
    deleteOrg,
    // delLeaderForOrg,
  }
  commands[command] && commands[command](row)
}

// 点击部门设置负责人
// const setLeaderForOrg = (row: OrgNode): void => {
//   setLeaderDialog.value?.openDialog({ ...row }, 'organization')
// }

// 点击用户设置负责人
// const setLeaderOnUser = async (row: UserNode): Promise<void> => {
//   const { orgName, id } = curOrgInfo.value || {}
//   const flag = await pConfirm(`确定要将${row.realName}设为${orgName}的负责人吗？`, $t('设置负责人'))
//   if (!flag) return

//   if (!id) return

//   const params = {
//     orgId: id,
//     userCode: row.userCode,
//   }
//   tableLoading.value = true
//   const { code = '' } = await setLeaderReq(params).finally(() => {
//     tableLoading.value = false
//   })
//   if (String(code) === '200') {
//     ElMessage.success($t('操作成功'))
//     refreshPageInfo()
//   }
// }

const resetOrgName = (orgNode: OrgNode): void => {
  const data = {
    parentId: orgNode.parentId,
    orgId: orgNode.id,
    orgName: orgNode.orgName,
    rootFlag: orgNode.rootFlag ? 1 : 0,
  }
  addOrgDialog.value.openDialog(data)
}

// 删除组织
const deleteOrg = (orgNode: OrgNode): void => {
  ElMessageBox.confirm($t(`确认要删除${orgNode.orgName}吗`), $t('删除组织'), {
    confirmButtonText: $t('确认'),
    cancelButtonText: $t('取消'),
    confirmButtonClass: 'el-button--danger',
    customClass: 'cus-message-box warn',
    showClose: false,
  })
    .then(async () => {
      orgLoading.value = true
      const orgIdList = []
      orgIdList.push(orgNode.id)
      try {
        if (orgNode.children && orgNode.children.length > 0) {
          orgNode.children.forEach((item: OrgNode) => {
            orgIdList.push(item.id)
          })
        }
        const result = await deleteOrgRequest({ orgIdList }).finally(() => {
          orgLoading.value = false
        })
        if (result.code === RESPONSE_CODE_SUCCESS) {
          curOrgInfo.value = orgList.value[0]
          currentOrgId.value = orgList.value[0].id
          currentOrgPath.value = orgList.value[0].orgIdPath
          refreshOrgList()
          ElMessage.success($t('删除成功'))
          refreshUserList()
        }
      } catch (error) {
        ElMessage.error($t('删除失败，请稍后重试'))
        return
      }
    })
    .catch(() => {})
}

const importHandle = () => {
  importUserDialog.value.openDialog()
}

const addNewUser = async () => {
  const selectOrg = (currentOrgPath.value || '').split(',')
  if (selectOrg.length) selectOrg.shift()
  const data = {
    operateType: 0,
    selectOrg: selectOrg,
  }
  await searchRoleList()
  addUserDialog.value.openDialog(data)
}

const deleteUser = (userNode: IUserListItem) => {
  ElMessageBox.confirm($t('确定删除该用户吗？'), $t('删除确认'), {
    confirmButtonText: $t('确定'),
    cancelButtonText: $t('取消'),
    type: 'warning',
  })
    .then(async () => {
      const result = await deleteUserRequest({ userIdList: [userNode.userId] })
      if (result.code === RESPONSE_CODE_SUCCESS) {
        ElMessage.success($t('删除成功'))
        searchUserList()
      } else {
        ElMessage.error(result.message || $t('删除失败，请稍后重试'))
      }
    })
    .catch(() => {
      // 取消删除
    })
}

const editUser = async (row: IUserListItem) => {
  // router.push({
  //   path: '/manage/userDetail',
  //   query: { code: row.userCode },
  // })
  await searchRoleList()
  const data = {
    operateType: 1,
    userId: row.userId,
    realName: row.realName,
    userName: row.userName,
    roleCode: row.roleCodeList,
    orgIdList: (row.orgIdListString || '').split(','),
    mainDepartment: row.mainDepartment,
  }
  addUserDialog.value.openDialog(data)
}

const handleSelectionChange = (selections: IUserListItem[]): void => {
  selectUserList.value = selections.map((user: IUserListItem) => user.userCode)
}

// const changeDep = () => {
//   if (!selectUserList.value.length) return ElMessage.error($t('请勾选选择需要移动部门的人员'))

//   changeDepartmentDialog.value.openDialog({
//     orgId: currentOrgId.value,
//     userCodeList: selectUserList.value.slice(),
//   })
// }

// const delLeaderOnUser = async (row: UserNode, params: any): Promise<void> => {
//   const flag = await pConfirm(`确定将${row.realName}从负责人中移除?`, $t('移除负责人'))
//   if (flag) {
//     await delOrgLeader({ orgId: params.orgId })
//     refreshPageInfo()
//   }
// }

// const delLeaderForOrg = async (row: OrgNode): Promise<boolean | undefined> => {
//   const flag = await pConfirm(`确定将${row.leaderName}从负责人中移除?`, $t('移除负责人'))
//   if (flag) {
//     await delOrgLeader({ orgId: row.orgId || '' })
//     refreshOrgList()
//     return false
//   }
// }

const searchRoleList = async () => {
  const params = {
    data: { roleName: '', startLastUpdateDate: '', endLastUpdateDate: '' },
    page: 1,
    pageSize: 1000,
  }
  const res = await queryPageRoleList<IQueryPageRoleList>(params)
  if (res.code == RESPONSE_CODE_SUCCESS) {
    //  const { result } = res.data || {};
    organizationStore.setRoleList(res.data.list || [])
    //  commit('set_roleSelectList', result || []);
    //  const roleCodeMap = formatListFunc({
    //    obj: result,
    //         key: 'roleCode',
    //         value: 'roleName',
    //       });
    //       commit('set_roleSelectMap', roleCodeMap);
    //     }
    //   });
  }
}

// 生命周期
onMounted(async () => {
  // initHandle()
  await searchOrgList()
})
</script>
<style lang="scss" scoped>
.organization-page {
  height: calc(100vh - 56px);
  padding-top: 16px;
  background: var(--bg-color);
  .page-title {
    height: 24px;
    padding: 0 16px;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    color: #000;
  }
  .page-content {
    height: calc(100% - 20px);
  }
  .left-container {
    float: left;
    width: 362px;
    height: 100%;
    .search-container {
      padding-left: 16px;
      margin-bottom: 16px;
    }
    .tree-container {
      height: calc(100% - 56px);
      padding-top: 8px;
      overflow-x: hidden;
      border-right: 1px solid #e9e9e9;
    }
    .organ-add-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 38px;
      margin-right: 19px;
      margin-left: 16px;
      font-size: 14px;
      color: var(--main-bg);
      cursor: pointer;
      border: 1px dashed #d4d4d4;
      border-radius: 4px;
      transition: 0.3s ease-in-out;
      .btn-icon {
        margin-right: 2px;
        margin-bottom: 1px;
      }
      &:hover {
        border: 1px solid var(--main-bg);
      }
    }
  }
  .right-container {
    float: left;
    width: calc(100% - 362px);
    height: 100%;
    .action-box {
      position: relative;
      padding-left: 24px;
    }
    .right-side-btn {
      position: absolute;
      top: 0;
      right: 11px;
    }
    .user-search {
      width: 320px;
      height: 32px;
    }
    .table-container {
      position: relative;
      display: flex;
      flex-direction: column;
      height: calc(100% - 6px);
      padding: 17px 11px 40px 23px;
      .el-table {
        flex: auto;
      }
    }
    .foot-container {
      position: absolute;
      bottom: 0;
      left: 0;
      display: flex;
      flex-direction: row-reverse;
      align-items: center;
      width: 100%;
      height: 40px;
      padding-right: 0;
    }
  }
  .custom-primary-hover {
    color: var(--main-bg);
    background: transparent;
    border-color: var(--main-bg);
    &:hover {
      color: var(--bg-color);
      background: var(--main-bg);
    }
  }
  .custom-table-class {
    width: 100%;
    height: 100%;
    :deep(th .cell) {
      overflow: hidden;
      font-size: 14px;
      white-space: nowrap;
    }
    :deep(.cell) {
      font-size: 14px;
    }
  }
  .organ-tree {
    position: relative;
    :deep(.el-tree-node__content) {
      position: relative;
      height: 38px;
      &::before {
        width: 10px;
        height: 100%;
        content: '';
      }
    }
    .el-tree-node__label {
      position: relative;
      display: flex;
      align-items: center;
      width: calc(100% - 30px);
      height: 100%;
      padding-right: 50px;
      .node-action {
        position: absolute;
        top: 0;
        right: 8px;
        bottom: 0;
        display: flex;
        justify-content: flex-end;
        width: 50px;
        .btn {
          font-size: 14px;
          color: #bdbdbd;
          &:hover {
            color: var(--main-bg);
          }
        }
        .el-button + .el-button {
          margin-left: 5px;
        }
      }
    }
    .edit-input {
      width: 100%;
      font-size: 0;
    }
    .label-info {
      display: inline-block;
      width: 100%;
      font-size: 13px;
      color: #606266;
      word-break: break-all;
      white-space: pre-wrap;
      cursor: pointer;
      &.primary {
        color: var(--main-bg);
      }
    }
  }
}
.organ-tree {
  .el-tree-node.is-drop-inner > .el-tree-node__content .el-tree-node__label {
    background-color: #f5f7fa;
    .label-info {
      color: var(--main-bg);
    }
  }
}
:deep(.el-scrollbar) {
  height: 100%;
  .scrollbar-wrapper {
    overflow: hidden auto;
  }
}

@media screen and (width <= 1300px) {
  .organization-page {
    .left-container {
      width: 260px;
    }
    .right-container {
      width: calc(100% - 260px);
    }
  }
}
</style>
