<template>
  <div class="chat-container">
    <div class="chat-container-header">
      <div class="chat-container-header-title">{{ title }}</div>
    </div>
    <div class="chat-container-body">
      <div class="chat-container-body-conversation">
        <div class="conversation-top">
          <el-scrollbar ref="scrollbarRef" class="conversation-scrollbar" :style="getContainerSty" @scroll="onScroll">
            <div class="chat-main-container">
              <div v-if="chatMsgs.length == 0" style="padding-top: 16px; text-align: left">
                <el-skeleton :rows="15" animated />
              </div>
              <div v-else class="conversation">
                <div
                  v-for="item in chatMsgs"
                  :key="item.id"
                  :class="item.chatRole == ChatRoleEnum.QUESTION ? 'sent' : 'received'"
                >
                  <div
                    v-if="item.chatRole == ChatRoleEnum.QUESTION && item.chatAttachInfos && item.chatAttachInfos.length"
                    class="attach-container"
                  >
                    <div v-for="(attach, index) in item.chatAttachInfos" :key="index" class="chat-attach">
                      <div v-if="!attach.isImg()" class="attach-item">
                        <Icons :name="attach.getFileSuffix()" width="2rem" height="2rem" />
                        <div class="attach-item-right">
                          <div class="inner-title">{{ attach.fileName }}</div>
                          <div class="inner-desc">
                            {{ attach.getFileSuffix() == 'web' ? attach.suffixName : attach.getFileSize() }}
                          </div>
                        </div>
                      </div>
                      <div v-else class="attach-item-img-container">
                        <img v-if="!attach.url" src="/src/assets/icons/svg/attach-cover.svg" alt="" />
                        <img v-else :src="attach.url" />
                      </div>
                    </div>
                  </div>
                  <div
                    v-if="item.chatRole == ChatRoleEnum.QUESTION && (item.chatContent || item.label)"
                    class="chat-item chat-ask"
                  >
                    {{ item.chatContent }}
                  </div>

                  <div v-if="item.chatRole == ChatRoleEnum.ANSWER">
                    <div class="chat-item chat-answer">
                      <Message
                        :chat-message="item"
                        @thought-visiable="
                          (visiable: boolean) => {
                            item.showThoughtProcessBar = visiable
                          }
                        "
                        @get-content="
                          (content: any) => {
                            item.copyContent = content
                          }
                        "
                        @focus-detail="
                          (num: string) => {
                            focusDetail(item, num)
                          }
                        "
                      />
                    </div>
                    <div v-if="item.isLast && item.chatContent">
                      <Tools v-if="item.showTools" ref="refTools" :chat-id="currentChatId" :chat-message="item" />
                      <!-- <div class="suggest-result">
                        <ul>
                          <li v-for="(suggest, index) in item.suggest" :key="index" @click="followUp(suggest)">
                            {{ suggest }}
                          </li>
                        </ul>
                      </div> -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-scrollbar>
          <div class="conversation-bottom">
            <div class="sender-box">
              <TextInputer
                ref="editorRef"
                :innerheight="68"
                :size="'attach-input-flow'"
                :hook="checkLoginStatus"
                @send="send"
                @stop="stop"
              />
            </div>
            <p class="agent-tip">{{ $t('以上内容由人工智能模型生成, 仅作参考') }}</p>
          </div>
        </div>
      </div>

      <div class="chat-container-body-references" v-show="quoteVisable">
        <References
          ref="referencesRef"
          @close="
            () => {
              quoteVisable = false
            }
          "
        />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, ref, nextTick, onMounted, onBeforeUnmount } from 'vue'
import { ChatRoleEnum, ChatStatusEnum, ChatMessage, ChatCommand } from '@/stores/modules/chat/helper'
import TextInputer from '@/components/chat/textInputer.vue'
import { useChatStore, useUserStore } from '@/stores'
import Tools from './com/tools.vue'
import ChatApi from '@/services/chat'
import { uuid } from '@/utils'
import Message from './com/message.vue'
import { BtnStatusEnum, RESPONSE_CODE_SUCCESS } from '@/constants'
import { ElMessage as Msg } from 'element-plus'
import References from './com/references.vue'
import { throttle } from 'lodash-es'
import { useRoute, useRouter, onBeforeRouteUpdate } from 'vue-router'
import type { IChatItem, IChatMessage } from '@/types/chat'
import { useChatService } from './com/chat'
import { $t } from '@/utils/i18n'

const router = useRouter()
const route = useRoute()
const { chatMsgs, difyConversation, abortConversation } = useChatService()
const chatStore = useChatStore()
const userStore = useUserStore()
const currentChatId = ref('')
const title = ref('')
const editorRef = ref()
const quoteVisable = ref(false)
const userScrolling = ref(false)
const scrollbarRef = ref()
const scrollElement = ref<HTMLElement>()

// 滚动事件处理函数
const handleWheel = () => {
  userScrolling.value = true
}

const getContainerSty = computed(() => {
  return {
    '--transition-x': `${!showSide.value ? '0rem' : '-15rem'}`,
    transform: ` translateX(var(--transition-x))`,
    height: '100%',
    width: `calc((100%) * 0.9)`,
    'padding-bottom': '1rem',
    margin: '0 auto',
  }
})

const loadChatMsgs = async (chatId: string) => {
  try {
    const { code, data, message } = await ChatApi.getChatDetail<IChatItem>(chatId)
    if (RESPONSE_CODE_SUCCESS != code) {
      Msg.error(message)
      setTimeout(() => {
        router?.push('/chat/')
      }, 1000)
      return false
    }
    title.value = data.chatTitle.length > 20 ? data.chatTitle.substring(0, 20) + '...' : data.chatTitle
    const chatActive = chatStore.chatActive
    const question = chatActive.getQuestion()
    const attachInfo = chatActive.getChatAttachInfo()
    const thinking = chatActive.getThinking()
    const webSearch = chatActive.getWebSearch()

    const { data: chatData } = await ChatApi.chatMessages<IChatMessage[]>(chatId)

    const processData: ChatMessage[] = chatData.map((item) => {
      const msg = ChatMessage.createFromResponse(item)
      if (msg.chatContent) msg.showTools = true
      return msg
    })
    let isDeepSeek = 0
    let isWebSearch = 1
    const finalData = processData.map((item, index) => {
      if (item.chatRole === 'assistant') {
        const target = processData[index - 1]
        item.isDeepSeek = target.isDeepSeek
        isDeepSeek = target.isDeepSeek
        item.isWebSearch = target.isWebSearch
        isWebSearch = target.isWebSearch
      }
      return item
    })
    chatMsgs.value = finalData
    console.log(chatMsgs.value, 'chatMsgs value after loadChatMsgs')
    isDeepSeek = thinking || isDeepSeek
    isWebSearch = webSearch || isWebSearch
    editorRef.value.setIsDeepSeek(isDeepSeek)
    editorRef.value.setIsInternetSearch(isWebSearch)
    if (finalData.length == 0) {
      const chatCmd = new ChatCommand()
      chatCmd
        .setChatId(chatId)
        .setContent(question)
        .setThinking(isDeepSeek)
        .setWebSearch(isWebSearch)
        .setChatAttachInfo(attachInfo)
      addConversation(chatCmd)
    } else {
      if (
        [
          ChatStatusEnum.PREPARE,
          ChatStatusEnum.PROGRESS,
          ChatStatusEnum.FILE_CONTENT_EXTRACTOR,
          ChatStatusEnum.LAW_SEARCHING,
          ChatStatusEnum.CASE_SEARCHING,
          ChatStatusEnum.WEB_SEARCHING,
          ChatStatusEnum.ARTICLE_SEARCHING,
          ChatStatusEnum.DEEPTHINKING,
          ChatStatusEnum.RUNNING,
        ].includes(chatMsgs.value[chatMsgs.value.length - 1].status)
      ) {
        editorRef.value.setBtnStatus(BtnStatusEnum.PROCESS)
      }
    }

    chatMsgs.value = finalData
    for (let i = 0; i < chatMsgs.value.length; i++) {
      if (chatMsgs.value[i].chatAttachInfos && chatMsgs.value[i].chatAttachInfos.length > 0) {
        for (let j = 0; j < chatMsgs.value[i].chatAttachInfos.length; j++) {
          if (chatMsgs.value[i].chatAttachInfos[j].isImg()) {
            await ChatApi.getChatFile(chatMsgs.value[i].chatAttachInfos[j].fileKey).then((res) => {
              const base64 = btoa(new Uint8Array(res).reduce((s, b) => s + String.fromCharCode(b), ''))
              chatMsgs.value[i].chatAttachInfos[j].url = `data:image/jpeg;base64,${base64}`
            })
          }
        }
      }
    }

    scrollThrottle()
    const lastMessage = chatMsgs.value[chatMsgs.value.length - 1]
    lastMessage.isLast = true
  } catch (error: any) {
    ElMessage.error(error.message || $t('加载对话失败'))
  }
}

const checkLoginStatus = () => {
  return new Promise((resolve) => {
    if (userStore.getToken()) {
      resolve(true)
    } else {
      resolve(false)
    }
  })
}

// 滚动到底部方法
const scrollToBottom = () => {
  // 通过 wrapRef 拿到真实滚动容器
  const wrap = scrollbarRef.value?.wrapRef
  if (wrap) {
    wrap.scrollTo({
      top: wrap.scrollHeight,
      behavior: 'smooth', // 平滑滚动
    })
  }
}

const scrollThrottle = throttle(scrollToBottom, 200)

//对话详情页继续发起对话是否清除输入框状态
const isReqed = ref(false)
const addConversation = async (chatCommand: ChatCommand) => {
  editorRef.value.setBtnStatus(BtnStatusEnum.PROCESS)
  const prev = chatMsgs.value[chatMsgs.value.length - 1]
  if (prev) {
    prev.isLast = false
  }
  chatMsgs.value.push(
    ChatMessage.createByUser({
      chatRole: ChatRoleEnum.QUESTION,
      chatContent: JSON.stringify({
        content: chatCommand.content,
        chatAttachInfos: chatCommand.chatAttachInfo,
      }),
      id: uuid(),
      chatId: chatCommand.chatId,
      isDeepSeek: chatCommand.thinking,
    }),
  )
  chatMsgs.value.push(
    ChatMessage.createByUser({
      chatRole: ChatRoleEnum.ANSWER,
      chatContent: '',
      id: uuid(),
      chatId: chatCommand.chatId,
      isDeepSeek: chatCommand.thinking,
    }),
  )
  userScrolling.value = false
  scrollThrottle()
  editorRef.value.setContent('')
  editorRef.value.setBtnStatus(BtnStatusEnum.PROCESS)
  await difyConversation(
    chatCommand.chatId,
    {
      content: chatCommand.content,
      attachInfo: chatCommand.chatAttachInfo,
      thinking: chatCommand.thinking,
      webSearch: chatCommand.webSearch,
      searchTypes: chatCommand.searchTypes,
      docTypeCode: chatCommand.docTypeCode,
    },
    chatMsgs.value[chatMsgs.value.length - 1].id,
    // answerMessage.id,
    (isCompleted) => {
      if (!isReqed.value) {
        isReqed.value = true
        editorRef.value.reset()
      }
      if (isCompleted) {
        chatStore.chatActive.removeChatActiveInfo()
        const text = editorRef.value.getContent()
        if (editorRef.value && !text) {
          editorRef.value.setBtnStatus(BtnStatusEnum.UN_READY)
        } else if (editorRef.value && text) {
          editorRef.value.setBtnStatus(BtnStatusEnum.READY)
        }
      }
      if (!userScrolling.value) {
        scrollThrottle()
      }
    },
  )
}
const onScroll = () => {
  scrollThrottle.cancel()
}
const showSide = ref(false)
const referencesRef = ref()
const focusDetail = (chatMessage: any, artId: string) => {
  quoteVisable.value = true
  referencesRef.value.setData(chatMessage.cache, artId)
}

const stop = async () => {
  await abortConversation(chatMsgs.value.length - 1)
}

const send = (chatCmd: ChatCommand) => {
  chatCmd.setChatId(currentChatId.value)
  addConversation(chatCmd)
}

const loadPageData = () => {
  chatStore.chatActive.setChatId(currentChatId.value)
  loadChatMsgs(currentChatId.value)
}

onMounted(() => {
  currentChatId.value = route.params.chatId as string
  loadPageData()

  // 等待下一个tick，确保DOM渲染完成
  nextTick(() => {
    // 获取 el-scrollbar 组件的实际滚动容器
    scrollElement.value = scrollbarRef.value?.wrapRef
    if (scrollElement.value) {
      scrollElement.value.addEventListener('wheel', handleWheel)
    }
  })
})

onBeforeRouteUpdate((to, from) => {
  if (to.path !== from.path && to.params.chatId) {
    currentChatId.value = to.params.chatId as string
    editorRef.value.setContent('')
    loadPageData()
  }
})

onBeforeUnmount(() => {
  if (scrollElement.value) {
    scrollElement.value.removeEventListener('wheel', handleWheel)
  }
})
</script>
<style lang="scss" scoped>
:deep(.el-scrollbar__bar.is-vertical) {
  display: none;
}
:deep(.el-loading-mask) {
  --el-mask-color: f9f9fb;
}
:deep(.el-loading-spinner) {
  --el-loading-spinner-size: 1.5rem;
}
.chat-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 0 1rem;
  color: var(--iterms-text-title-color);
  .chat-container-header {
    height: 3.375rem;
    padding-top: 1rem;
    .chat-container-header-title {
      font-size: 1rem;
      font-weight: 500;
      line-height: 1.375rem;
    }
  }
  .chat-container-body {
    display: flex;
    flex: 1;
    width: 100%;
    .chat-container-body-conversation {
      position: relative;
      flex: 1;
      flex-direction: column;
      min-width: 22.5rem;
      .conversation-top {
        display: flex;
        justify-content: center;
        height: calc(100vh - 12rem);
        .conversation-bottom {
          position: absolute;
          bottom: 0.75rem;
          display: flex;
          flex-direction: column;
          width: 100%;
          min-width: min(45rem, 100%);
          max-width: 45rem;
          margin: 0 auto;
          .sender-box {
            position: relative;
          }
          .agent-tip {
            margin-top: 0.75rem;
            font-size: 0.75rem;
            color: var(--sidebar-line);
            text-align: center;
          }
        }
      }
    }
    .chat-container-body-references {
      display: flex;
      width: 360px;
      height: 100%;
    }
    .chat-main-container {
      max-width: 45rem;
      height: fit-content;
      margin: 0 auto;
      .conversation {
        display: flex;
        flex-direction: column;
        padding-bottom: 3.5rem;
        .suggest-result {
          .tip {
            color: rgb(0 0 0 / 50%);
          }
          > ul {
            padding: 0;
            font-size: 0.875rem;
            font-weight: 400;
            line-height: 1.5rem;
            color: rgb(0 0 0 / 100%);
            list-style: none;
          }
          > ul li {
            width: fit-content;
            padding: 0.5rem 0.75rem;
            margin-bottom: 0.5rem;
            cursor: pointer;
            border: 1px solid var(--page-header-line);
            border-radius: 0.5rem;
          }
        }
        .suggest-result::before {
          display: block;
          content: '';
        }
        .attach-item-img {
          display: flex;
          align-items: center;
          width: fit-content;
          border-radius: 0.25rem;
          > img {
            width: 4.25rem;
            height: 4.25rem;
            border-radius: 0.25rem;
          }
        }
        .attach-item {
          display: flex;
          align-items: center;
          width: fit-content;
          padding: 0.5rem;
          text-align: right;
          background-color: #f2f3f666;
          border-radius: 0.5rem;
          .attach-item-right {
            width: fit-content;
            margin-left: 0.5rem;
            .inner-title {
              width: 9rem;
              height: 1.25rem;
              overflow: hidden;
              text-overflow: ellipsis;
              font-size: 0.875rem;
              font-weight: 400;
              line-height: 1.25rem;
              color: rgb(0 0 0 / 88%);
              text-align: left;
              white-space: nowrap;
            }
            .inner-desc {
              display: flex;
              align-items: center;
              height: 1rem;
              overflow: hidden;
              text-overflow: ellipsis;
              font-size: 0.75rem;
              font-weight: 300;
              line-height: 1rem;
              color: rgb(0 0 0 / 45%);
              white-space: nowrap;
            }
          }
        }
        .attach-item-fail {
          border: 1px solid var(--is-color-e6555e);
          .attach-item-right {
            .inner-desc {
              color: var(--is-color-e6555e);
            }
          }
        }
      }
    }
  }
}
.sent {
  display: flex;
  flex-direction: column;
  align-self: flex-end;
  margin-top: 1.375rem;
  .attach-container {
    display: flex;
    justify-content: end;
    height: 3.375rem;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    .chat-attach {
      margin-left: 0.25rem;
      .attach-item-img-container {
        height: 100%;
        img {
          width: 3.375rem;
          height: 3.375rem;
          border-radius: 0.5rem;
        }
      }
    }
  }
  .chat-ask {
    align-self: end;
    padding: 0.75rem;
    font-size: 0.875rem;
    color: var(--minor-font);
    word-break: break-all;
    overflow-wrap: break-word;
    white-space: pre-wrap;
    background: var(--is-color-773bef1a);
    border-radius: 0.5rem;
    border-top-right-radius: 0;
  }
}
.received {
  align-self: flex-start;
}
.chat-item {
  max-width: 720px;
}
.chat-answer {
  width: fit-content;
  padding-top: 1rem;
  text-align: left;
  .search-result {
    display: flex;
    align-items: center;
    width: fit-content;
    height: 1.5rem;
    padding: 0.5rem 1rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: rgb(16 22 95 / 100%);
    background-color: rgb(248 248 255 / 100%);
    border: 1px solid rgb(248 248 255 / 100%);
    border-radius: 0.5rem;
  }
  .search-result > div {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.chat-footer-contaniner {
  display: flex;
  justify-content: center;
  width: 90%;
  max-width: 45rem;
  margin: 0 auto;
  .sender-btns {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    .btn {
      display: flex;
      align-items: center;
      padding: 0.375rem 0.75rem;
      margin-right: 0.5rem;
      cursor: pointer;
      background: var(--bg-color);
      border: 1px solid rgb(232 232 232 / 100%);
      border-radius: 0.5rem;
      > span:first-child {
        display: flex;
        margin-right: 0.375rem;
        vertical-align: middle;
      }
    }
  }
}
.conversation {
  display: flex;
  flex-direction: column;
}
.send {
  align-self: flex-end;
}
.grid-container {
  display: flex;
  flex-direction: column;
}
.side {
  width: 0;
  opacity: 0;
  transform: translateX(500px);
  transition: all 0.3s ease;
}
.open {
  width: 28.75rem;
  opacity: 1;
  transform: translateX(0);
}
</style>
