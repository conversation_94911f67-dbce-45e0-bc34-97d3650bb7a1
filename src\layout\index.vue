<script setup lang="ts">
import Sidebar from './components/Sidebar.vue'

import { useUserStore } from '@/stores'
import LoginDialog from '@/components/LoginDialog.vue'
const userStore = useUserStore()

const route = useRoute()
const isSingle = computed(() => route.meta.single)
const refSidebar = ref<InstanceType<typeof Sidebar>>()

// 记录上一次的isSingle状态
const prevIsSingle = ref(isSingle.value)

const initUserInfo = async () => {
  try {
    if (userStore.token) {
      await userStore.loadUserInfo()
      refSidebar.value && refSidebar.value.getChats()
    }
  } catch (err) {
    console.error(err)
  } finally {
  }
}
const router = useRouter()

const currentPermission = route.meta?.permission ?? undefined

const initAuth = () => {
  const authList = userStore.getMenuList()
  if (currentPermission) {
    const hasAuth = authList.some((item) => item === currentPermission)
    if (!hasAuth) {
      // 没有权限，重定向到首页
      // router.push('/404')
    }
  }
}

onMounted(async () => {
  // 初始化用户信息
  await initUserInfo()
  if (userStore.token) {
    await initAuth()
  }
})

// 监听isSingle变化，检测从二级页面返回到一级页面
watch(
  () => isSingle.value,
  async (newIsSingle) => {
    if (prevIsSingle.value === true && newIsSingle === false) {
      nextTick(async () => {
        refSidebar.value && (await refSidebar.value.getChats())
      })
    }
    prevIsSingle.value = newIsSingle
  },
)
</script>

<template>
  <div class="app-wrapper" :class="{ 'singe-wrapper': isSingle }">
    <Sidebar ref="refSidebar" v-if="!isSingle" />
    <div class="app-main" id="app-main">
      <router-view class="app-conetent review-wrap" />
    </div>
    <LoginDialog />
  </div>
</template>

<style lang="scss" scoped>
.app-wrapper {
  display: flex;
  height: 100vh;
  .app-main {
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: hidden;
  }
  .app-conetent {
    flex: 1;
    height: 0;
  }
  .review-wrap {
    display: flex;
    width: 100%;
    height: 100%;
  }
}
.singe-wrapper {
  flex-direction: column;
  height: 100vh;
}
</style>
