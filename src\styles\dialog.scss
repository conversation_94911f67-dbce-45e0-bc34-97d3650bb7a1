.el-dialog {
  padding: 0;

  // 登录标题
  .login.el-dialog__header {
    border-bottom: 1px solid transparent;
    .el-dialog__headerbtn {
      padding: 24px 24px 0 0;
      .el-dialog__close {
        border: 1px solid transparent;
      }
      .el-dialog__close:hover {
        border: 1px solid transparent;
      }
    }
  }
  .task.el-dialog__header {
    height: 4.1875rem;
  }
  .task.el-dialog__body {
    height: calc(100vh - 18.75rem);
  }

  // 标题
  .el-dialog__header {
    height: 47px;
    padding: 11px 16px;
    border-bottom: 1px solid var(--page-header-line);

    // 名称
    .el-dialog__title {
      display: inline-block;
      height: 24px;
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
    }

    // 关闭按钮
    .el-dialog__headerbtn {
      width: 48px;
      height: 48px;
      .el-dialog__close {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        color: var(--is-color-a2a1a9);
        border: 1px solid var(--page-header-line);
        border-radius: 4px;
      }
      .el-dialog__close:hover {
        color: var(--is-color-a2a1a9);
        background-color: var(--input-bg);
        border: 1px solid;
        border-color: var(--page-header-line);
      }
    }
    .el-dialog__headerbtn:hover {
      .el-dialog__close {
        color: var(--is-color-a2a1a9);
      }
    }
  }
  .el-dialog__body {
    padding: 16px;
  }
  .el-dialog__footer {
    padding: 16px;
    border-top: 1px solid var(--page-header-line);
  }
  .el-button + .el-button {
    margin-left: 16px;
  }
}

// .login .el-dialog {
//   // background-color: pink;
//   // 标题
//   background-color: pink;
//   .el-dialog__header {
//     border-bottom: 1px solid transparent;

//     // 关闭按钮
//     .el-dialog__headerbtn {
//       width: 47px;
//       height: 47px;
//       .el-dialog__close {
//         display: flex;
//         align-items: center;
//         justify-content: center;
//         width: 24px;
//         height: 24px;
//         color: var(--is-color-a2a1a9);

//         // border: 1px solid var(--page-header-line);
//         // border-radius: 4px;
//       }
//       .el-dialog__close:hover {
//         color: var(--is-color-a2a1a9);
//         background-color: var(--input-bg);

//         // border: 1px solid;
//         // border-color: var(--page-header-line);
//       }
//     }
//     .el-dialog__headerbtn:hover {
//       .el-dialog__close {
//         color: var(--is-color-a2a1a9);
//       }
//     }
//   }
// }
