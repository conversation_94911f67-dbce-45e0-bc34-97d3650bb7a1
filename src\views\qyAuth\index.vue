<template>
  <div></div>
</template>
<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { ElLoading, ElMessage } from 'element-plus'
import { queryOpenapi } from '../../services/app'
import { useUserStore } from '@/stores'
import $t from '@/utils/i18n'

// 定义组件名称
defineOptions({
  name: 'QyAuth',
})

// 响应式数据
const loading = ref<any>(null)

// 获取路由和用户store
const router = useRouter()
const userStore = useUserStore()

// 获取URL查询参数的工具函数
function getQueryString(name: string): string | null {
  const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
  const r = window.location.search.substring(1).match(reg)
  if (r != null) return decodeURIComponent(r[2])
  return null
}

// 处理授权逻辑
async function handleAuth() {
  // 重置用户状态（如果需要的话，这里可能需要调用相应的store方法）
  // userStore.resetState()

  loading.value = ElLoading.service({ fullscreen: true })

  const code = getQueryString('code')
  console.log('dd', code)

  if (code) {
    try {
      const { data }: any = await queryOpenapi({ code })
      console.log('data', data)
      userStore.setQyToken(data.token)
      const redirectPath = data.redirectPath
      router.replace({ path: redirectPath })
    } catch (error) {
      console.log(error)
      showError()
    }
  } else {
    showError()
  }
}
const showError = () => {
  ElMessage({
    message: $t('登录失败了，请联系管理员！'),
    type: 'error',
    duration: 0,
  })
  loading.value?.close()
}
// 组件挂载时执行授权逻辑
onMounted(() => {
  handleAuth()
})

// 组件卸载前清理loading
onBeforeUnmount(() => {
  loading.value?.close()
})
</script>
