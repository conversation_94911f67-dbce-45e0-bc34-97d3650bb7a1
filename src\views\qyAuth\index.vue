<template>
  <div></div>
</template>
<script>
import { Loading } from 'element-ui'
import { queryOpenapi } from '../../services/app'
import { useUserStore } from '@/stores'
const userStore = useUserStore()
export default {
  name: 'QyAuth',
  data() {
    return {
      path: '',
      loading: null,
    }
  },
  async created() {
    this.$store.dispatch('user/resetState')
    this.loading = Loading.service({ fullscreen: true })
    this.$once('hook:beforeDestroy', () => {
      this.loading && this.loading.close()
    })

    const code = getQueryString('code')
    console.log('dd', code)

    // this.path = decodeURIComponent(getQueryString('path') || '/');
    if (code) {
      try {
        const { data } = await queryOpenapi({ code })
        console.log('data', data)

        this.$store.dispatch('user/setQyToken', data.token)
        this.$store.dispatch('user/setUserInfo', data.userData)
        userStore.setToken(data)
        userStore.setIsLogin(true)
        userStore.setShowLoginBox(false)
        const path = data.redirectPath
        this.$router.replace({ path })
      } catch (error) {
        console.log(error)

        this.$message({ message: '登录失败了，请联系管理员！', type: 'error', duration: 0 })
        this.loading.close()
      }
    } else {
      this.$message({ message: '登录失败了，请联系管理员！', type: 'error', duration: 0 })
      this.loading.close()
    }
  },
}
function getQueryString(name) {
  const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
  const r = window.location.search.substr(1).match(reg)
  if (r != null) return unescape(r[2])
  return null
}
</script>
