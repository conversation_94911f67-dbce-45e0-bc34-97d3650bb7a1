<template>
  <svg
    aria-hidden="true"
    :width="svgWidth"
    :height="svgHeight"
    :fill="color"
    stroke="none"
    style="outline: none; border: none"
  >
    <use :href="symbolId" style="fill: currentcolor; stroke: none" />
  </svg>
</template>
<script lang="ts" setup>
import { computed } from 'vue'

const props = defineProps({
  prefix: {
    type: String,
    default: 'icon',
  },
  name: {
    type: String,
    required: true,
  },
  color: {
    type: String,
    default: '',
  },
  width: {
    type: String,
    default: '1rem',
  },
  height: {
    type: String,
    default: '1rem',
  },
})
const svgHeight = computed(() => {
  return parseFloat(props.height) * 16 + 'px'
})
const svgWidth = computed(() => {
  return parseFloat(props.width) * 16 + 'px'
})
const symbolId = computed(() => `#${props.prefix}-${props.name}`)
</script>

<style scoped>
svg {
  outline: none !important;
  border: none !important;
  stroke: none !important;
}
svg use {
  stroke: none !important;
}
</style>
