import { computed, onMounted, onUnmounted, ref } from 'vue'
import { getDisplayGrid, IUploadFile } from './helper'
import { ChatCommand } from '@/stores/modules/chat/helper'
import { getStringLengthComplex } from '@/utils'
import { PATH_URL, RESPONSE_CODE_SUCCESS, BtnStatusEnum, InputerStyleEnum } from '@/constants'
import { getCurrentInstance } from 'vue'
import { useUserStore } from '@/stores'
import { debounce } from 'lodash-es'
import type { InnerStyConfInter } from '@/types/chat'
import type { UploadRequestOptions } from 'element-plus'
import axios from 'axios'

import { $t } from '@/utils/i18n'
export function useTextInputerService() {
  const instance = getCurrentInstance()
  const userStore = useUserStore()
  const props = instance!.props
  const emits = instance!.emit
  const inputValue = ref('')
  const chatInputBoxRef = ref<HTMLDivElement>()
  const cacheFiles = ref<IUploadFile[]>([])
  const uploadFiles = ref<IUploadFile[]>([])
  const btnStatus = ref(BtnStatusEnum.UN_READY)
  const isDeepSeek = ref(false)
  const isWebSearch = ref(true)

  const displayOption = ref<InnerStyConfInter>({
    attachShow: false,
    grid: '',
    headerShow: false,
    uploadPopShow: true,
    uploadShow: false,
    lineShow: false,
    leftFooterBarShow: false,
  })

  const setOption = (key: InputerStyleEnum, option: Partial<InnerStyConfInter>) => {
    const { grid } = getDisplayGrid().get(key)
    Object.assign(displayOption, { grid, ...option })
  }

  const editorFocus = () => {
    if (chatInputBoxRef.value) {
      chatInputBoxRef.value.classList.add('input_active')
    }
  }
  const editorBlur = () => {
    if (chatInputBoxRef.value) {
      chatInputBoxRef.value.classList.remove('input_active')
    }
  }

  const defaultStyKey = ref(InputerStyleEnum.INNER_ONLY_FLOW)
  /**
   * 处理输入换行后，要改变输入框的样式
   * @param inner
   * @returns
   */

  const setContainerSty = (key: InputerStyleEnum, option = {}) => {
    defaultStyKey.value = key
    const conf = getDisplayGrid().get(key)
    Object.assign(conf, { ...option })
    displayOption.value = conf
    const wrapWidth = props.width as number
    const container = chatInputBoxRef.value!.querySelector('.input-container') as HTMLDivElement
    let innerInputWidth = 0
    if (container) {
      container.setAttribute(
        'style',
        `grid-template-areas:${displayOption.value.grid};--input-inner-height:${props.innerheight}px;`,
      )
      innerInputWidth = key.indexOf('flow') != -1 ? container.clientWidth - 24 : container.clientWidth - 120
    } else {
      innerInputWidth = key.indexOf('flow') != -1 ? wrapWidth - 24 : wrapWidth - 120
    }
    const editorWrap = chatInputBoxRef.value!.querySelector('.inner-text-content') as HTMLDivElement
    if (editorWrap) {
      editorWrap.style.width = innerInputWidth + 'px'
    }
  }

  watch(
    inputValue,
    debounce((newValue: string) => {
      if (btnStatus.value === BtnStatusEnum.PROCESS) return
      if (newValue) {
        btnStatus.value = BtnStatusEnum.READY
      } else {
        btnStatus.value = BtnStatusEnum.UN_READY
      }
    }, 100),
  )

  /**
   * 发送用户输入的消息
   *
   * 处理流程：
   * 1. 执行钩子函数(如果提供)，可能用于验证用户登录状态
   * 2. 获取编辑器内容并提取文本和标签
   * 3. 进行各种验证检查(空内容、字符长度限制等)
   * 4. 创建并配置聊天命令对象
   * 5. 触发发送事件并清除参数
   *
   * @returns {Promise<boolean|void>} 如果验证失败返回false，成功发送无返回值
   * @throws 可能抛出与钩子函数相关的异常
   */
  const send = async () => {
    const hook = props?.hook as (() => Promise<boolean>) | undefined
    if (hook) {
      const hookResult = await hook()
      if (!hookResult) {
        userStore.setIsLogin(false)
        userStore.setShowLoginBox(true)
        return false
      }
    }
    if (getStringLengthComplex(inputValue.value) > 5000) {
      ElMessage.warning($t('内容不能超过5000个字符'))
      return false
    }
    if (isReady.value) {
      const chatCmd = new ChatCommand()
      chatCmd
        .setContent(inputValue.value)
        .setThinking(isDeepSeek.value ? 1 : 0)
        .setWebSearch(isWebSearch.value ? 1 : 0)
        .setChatAttachInfo(cacheFiles.value)
      if (btnStatus.value == BtnStatusEnum.READY) {
        btnStatus.value = BtnStatusEnum.PROCESS
        await emits('send', chatCmd)
      }
    }
  }

  const clearParams = () => {
    inputValue.value = ''
    setTimeout(() => {
      cacheFiles.value.length = 0
    }, 10)
  }

  const stop = async () => {
    if (btnStatus.value == BtnStatusEnum.PROCESS) {
      if (inputValue.value) {
        btnStatus.value = BtnStatusEnum.READY
      } else {
        btnStatus.value = BtnStatusEnum.UN_READY
      }
    }
    emits('stop')
  }

  const isReady = computed(() => {
    return btnStatus.value == BtnStatusEnum.READY
  })

  const isUnReady = computed(() => {
    return btnStatus.value == BtnStatusEnum.UN_READY
  })

  const isStopable = computed(() => {
    return btnStatus.value == BtnStatusEnum.PROCESS
  })

  const customUpload = (options: UploadRequestOptions): Promise<unknown> => {
    const file = options.file
    const headers: Record<string, string> = {
      csrf_token: userStore.getToken(),
    }
    const tempFile = new IUploadFile({})
    tempFile.uid = file.uid
    tempFile.fileName = file.name
    tempFile.fileSize = file.size
    tempFile.fileSuffix = file.name.substring(file.name.lastIndexOf('.') + 1, file.name.length)
    tempFile.status = 'ready'
    tempFile.completed = false
    tempFile.type = file.type
    if (file.type.match(/^image/)) {
      const reader = new FileReader()
      reader.onload = (e: any) => {
        tempFile.url = e.target.result
      }
      reader.readAsDataURL(file)
    }
    uploadFiles.value.push(tempFile)
    const formData = new FormData()
    formData.append('file', file)
    const finded = uploadFiles.value.find((it) => it.uid === file.uid)
    cacheFiles.value.push(tempFile)
    axios
      .post(PATH_URL + '/chat/uploadFile', formData, {
        headers,
        onUploadProgress: (xhr) => {
          if (finded) {
            if (xhr.progress) {
              finded.percentage = xhr.progress * 100
            }
            finded.status = 'uploading'
          }
        },
      })
      .then(({ status, data }) => {
        if (status == 200) {
          if (data.code == RESPONSE_CODE_SUCCESS) {
            if (finded) {
              finded.status = 'success'
              finded.completed = true
              finded.fileKey = data.data.fileKey
              finded.fileSuffix = data.data.fileSuffix
            }
          } else {
            if (finded) {
              finded.status = 'fail'
              finded.completed = false
              finded.fileName = file.name
              finded.fileSize = file.size
              finded.fileSuffix = file.name.substring(file.name.lastIndexOf('.') + 1, file.name.length)
            }
          }
        } else {
          if (finded) {
            finded.fileName = file.name
            finded.fileSize = file.size
            finded.fileSuffix = file.name.substring(file.name.lastIndexOf('.') + 1, file.name.length)
            finded.status = 'fail'
            finded.completed = false
          }
          ElMessage.error($t('网络请求失败'))
        }
      })
      .catch((e) => {
        if (finded) {
          finded.fileName = file.name
          finded.fileSize = file.size
          finded.fileSuffix = file.name.substring(file.name.lastIndexOf('.') + 1, file.name.length)
          finded.status = 'fail'
          finded.completed = false
        }
        console.log('upload error' + e)
      })

    return Promise.resolve()
  }

  const removeFile = (file: IUploadFile) => {
    if (file) {
      uploadFiles.value = uploadFiles.value.filter((item) => item.uid != file.uid)
      cacheFiles.value = cacheFiles.value.filter((item) => item.uid != file.uid)
    }
  }

  /**
   * onMounted钩子中执行了以下初始化操作:
   * 1. 初始化编辑器实例(如果不存在)
   * 2. 设置输入框的宽度为传入的props.width
   * 3. 根据props.size设置输入容器样式
   * 4. 加载文档标签数据
   */
  onMounted(() => {
    if (chatInputBoxRef.value) {
      chatInputBoxRef.value.style.width = props.width + 'px'
    }
    if (props.size && typeof props.size === 'string') {
      setContainerSty(props.size as InputerStyleEnum)
    }
  })

  onUnmounted(() => {
    clearParams()
  })

  return {
    inputValue,
    clearParams,
    isDeepSeek,
    isWebSearch,
    chatInputBoxRef,
    btnStatus,
    isReady,
    isUnReady,
    setOption,
    editorFocus,
    editorBlur,
    send,
    stop,
    isStopable,
    setContainerSty,
    cacheFiles,
    displayOption,
    uploadFiles,
    customUpload,
    removeFile,
  }
}
