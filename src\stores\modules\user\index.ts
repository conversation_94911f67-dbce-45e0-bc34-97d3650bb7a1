import { LOGIN_SUCCESS, RESPONSE_CODE_SUCCESS, MEMBER_ID_KEY } from '@/constants'
import { defineStore } from 'pinia'
import { ref } from 'vue'
import UserApi from '@/services/user'
import type { IUserPermission } from '@/services/user'
import type { IGetQrCodeResponse, IGetQrCodeResult, IgetCurrentUserInfoResponse, IUserInfo } from '@/types/user'
import emitter from '@/utils/eventsBus'
import { UserInfo } from './helper'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

import { $t } from '@/utils/i18n'
export const useUserStore = defineStore(
  'userInfo',
  () => {
    const token = ref('')
    const qyToken = ref('')
    const router = useRouter()
    const isLogined = ref(false)
    const showLoginBox = ref(false)
    const userInfo = ref<IUserInfo>(new UserInfo())
    // const userHabits = ref({
    //   page: '',
    //   history: false,
    //   trigger: false,
    // })
    const menuList = ref<string[]>(['contractContent', 'ContractComparison', 'ContractExtract', 'QA']) // 默认值，登录前显示

    const setToken = (newToken: string) => {
      token.value = newToken
    }

    const setQyToken = (newQyToken: string) => {
      qyToken.value = newQyToken
    }

    const setMenuList = (newMenuList: string[]) => {
      menuList.value = newMenuList
    }

    const getMenuList = () => {
      return menuList.value
    }

    const fetchCurrentUserInfo = async () => {
      const res = await UserApi.getCurrentUserInfo<IgetCurrentUserInfoResponse>()
      if (res.code === RESPONSE_CODE_SUCCESS) {
        const existingCorpList = userInfo.value?.corpList // 保存现有的corpList
        userInfo.value = res.data.userInfo
        if (existingCorpList && userInfo.value) {
          userInfo.value.corpList = existingCorpList // 恢复corpList
        }
        if (res.data.memberInfo && userInfo.value) {
          userInfo.value.memberInfo = res.data.memberInfo
        }
      } else {
        throw new Error(res.message || $t('获取用户信息失败'))
      }
    }

    // 获取当前用户功能列表
    const getMenuCodeList = async () => {
      const res = await UserApi.getUserPermission<IUserPermission>()
      if (res.code === RESPONSE_CODE_SUCCESS) {
        setMenuList(res.data.menuCodeList)
      } else {
        throw new Error($t('获取功能列表失败'))
      }
    }

    const loadUserInfo = async () => {
      setMenuList([]) // 清空权限列表
      try {
        // 1. 先获取corpList
        const corpRes = await UserApi.getCorpList()
        if (corpRes.code !== RESPONSE_CODE_SUCCESS) {
          isLogined.value = false
          ElMessage({
            message: $t('获取企业列表失败'),
            type: 'error',
            duration: 3 * 1000,
          })
          return
        }
        // 2. 校验memberId
        let memberId = localStorage.getItem(MEMBER_ID_KEY)
        const corpList = Array.isArray(corpRes.data) ? corpRes.data : []
        // 确保userInfo.value不为null，如果为null则初始化
        if (!userInfo.value) {
          userInfo.value = new UserInfo()
        }
        userInfo.value.corpList = corpList
        const found = corpList.some((corp: any) => corp.id === memberId)
        if (!found) {
          localStorage.removeItem(MEMBER_ID_KEY)
          memberId = ''
        }
        try {
          await UserApi.switchCorp(memberId || '')
        } catch (e) {
          ElMessage({
            message: $t('切换团队失败'),
            type: 'warning',
            duration: 3 * 1000,
          })
        }
        // 3. 获取用户信息
        try {
          await fetchCurrentUserInfo()

          if (memberId) {
            // 获取用户功能列表
            await getMenuCodeList()
          } else {
            setMenuList(['contractContent', 'ContractComparison', 'ContractExtract', 'QA'])
          }
        } catch (error: any) {
          ElMessage.warning(error.message)
        }
      } catch (error: any) { }
    }
    // const getUserInfo = async () => {
    //   if (!userInfo.value.userId) {
    //     await loadUserInfo()
    //   }
    // }

    const setIsLogin = (flag: boolean) => {
      isLogined.value = flag
    }
    const setShowLoginBox = (flag: boolean) => {
      showLoginBox.value = flag
    }
    // const deregisterAccount = () => {
    //   userInfo.value = new UserInfo()
    //   session.remove(ITERMS_LOGIN_TK)
    //   router.replace({ path: '/' })
    // }
    const initUserInfo = () => {
      userInfo.value = new UserInfo()
      isLogined.value = false
      showLoginBox.value = false
      token.value = ''
      router.replace({ path: '/' })
    }
    // const logOut = async () => {
    //   const { code, message } = await UserApi.logOut()
    //   if (code === RESPONSE_CODE_SUCCESS) {
    //     isLogined.value = false
    //     showLoginBox.value = false
    //     userInfo.value = new UserInfo()
    //     session.remove(ITERMS_LOGIN_TK)
    //     Msg.success(message)
    //     router.replace({ path: '/' })
    //   } else {
    //     Msg.error(message)
    //   }
    // }
    const editNickName = async (nickName: string) => {
      userInfo.value!.nickName = nickName
      //   const { code, data } = await UserApi.editNickName<IUserInfo>(nickName)
      //   if (code === RESPONSE_CODE_SUCCESS) {
      //     userInfo.value.nickName = data.nickName
      //     ElMessage({
      //       message: '修改成功',
      //       type: 'success',
      //       duration: 3 * 1000,
      //     })
      //   }
      // } catch (error) {
      //   console.error(error)
      // }
    }
    const qrCode = ref('')
    const qrCodeId = ref('')
    const getQrCode = async () => {
      try {
        const { code, data } = await UserApi.getQrCode<IGetQrCodeResponse>()
        if (code === RESPONSE_CODE_SUCCESS) {
          qrCode.value = data.qrCode
          qrCodeId.value = data.qrCodeId
          isQrCodeExpired.value = false
        }
      } catch (error) {
        console.error(error)
      }
    }
    const isQrCodeExpired = ref(false)
    const getQrCodeLoginResult = async () => {
      const { code, message, data } = await UserApi.getQrCodeLoginResult<IGetQrCodeResult>(qrCodeId.value)
      if (code === RESPONSE_CODE_SUCCESS) {
        if (data.scanStatus == 1) {
          token.value = data.token || ''
          setIsLogin(true)
          setShowLoginBox(false)
          await loadUserInfo()
          ElMessage.success($t('登录成功'))
          emitter.emit(LOGIN_SUCCESS)
          stopScan()
        } else if (data.scanStatus == 2 || data.scanStatus == 3) {
          ElMessage.error($t('登录失败'))
          stopScan()
        }
      } else {
        isQrCodeExpired.value = true
        stopScan()
        getQrCode()
        startScan() //error(message)
        ElMessage({
          message: message,
          grouping: true,
          type: 'error',
        })
      }
    }
    const timer = ref<NodeJS.Timeout>()
    const startScan = () => {
      stopScan()
      timer.value = setInterval(() => {
        getQrCodeLoginResult()
      }, 3000)
    }
    const stopScan = () => {
      clearInterval(timer.value)
    }

    const logout = () => {
      token.value = ''
      userInfo.value = new UserInfo()
      isLogined.value = false
      showLoginBox.value = false
      // 清除本地存储
      localStorage.removeItem('user')
    }

    return {
      token,
      setToken,
      setQyToken,
      startScan,
      stopScan,
      editNickName,
      // getUserInfo,
      getQrCode,
      qrCode,
      // qrCodeId,
      isLogined,
      showLoginBox,
      userInfo,
      isQrCodeExpired,
      // logOut,
      setIsLogin,
      setShowLoginBox,
      // deregisterAccount,
      // userHabits,
      loadUserInfo,
      fetchCurrentUserInfo,
      initUserInfo,
      logout,
      getMenuCodeList,
      setMenuList,
      getMenuList,
    }
  },
  {
    persist: {
      key: 'userInfo',
      storage: localStorage,
      pick: ['userInfo', 'token'], // Persist only userInfo and token
    },
  },
)
