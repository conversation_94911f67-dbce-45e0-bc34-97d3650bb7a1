<script setup lang="ts">
import { useUserStore, useChatStore } from '@/stores'
import UserMenu from './UserMenu.vue'
import { isLocal } from '@/utils/config'
import { LOGIN_SUCCESS } from '@/constants'
import emitter from '@/utils/eventsBus'
import recentChatItem from '@/components/chat/recentChatItem.vue'
import ChatApi from '@/services/chat'
import type { IChatsListResponse, IChatItem } from '@/types/chat'
import { RESPONSE_CODE_SUCCESS } from '@/constants'
import { ChatItem } from '@/stores/modules/chat/helper'
import { $t } from '@/utils/i18n'

const userStore = useUserStore()
const chatStore = useChatStore()

// 获取当前用户功能列表
const permissionList = computed(() => userStore.getMenuList())

// 控制用户菜单浮层显示
const showUserMenu = ref(false)

interface MenuItem {
  label: string
  icon?: string
  iconActive?: string
  path: string
  permission?: string
  children?: MenuItem[]
}

const pageNo = 1
const pageSize = 15
emitter.on(LOGIN_SUCCESS, async () => {
  // 获取最近会话列表，并更新到store中
  const {
    code,
    data: { list },
  } = await ChatApi.getChatsList<IChatsListResponse>({ currentPageNo: pageNo, pageSize })
  if (RESPONSE_CODE_SUCCESS == code && list) {
    const tempList: ChatItem[] = []
    list.forEach((it: IChatItem) => {
      tempList.push(ChatItem.create(it))
    })
    chatStore.setHistoryChatList(tempList)
  }
})

// 使用计算属性来确保菜单项在语言切换时能够响应式更新
const menus = computed<MenuItem[]>(() => {
  const routes = router.getRoutes()

  // 添加 Permission 属性
  const getPermission = (path: string) => {
    const matchedRoute = routes.find((r) => r.path === path || r.path === `${path}/`)
    return matchedRoute?.meta?.permission as string | undefined
  }

  let baseMenus: MenuItem[] = []

  if (isLocal) {
    baseMenus = [
      {
        label: $t('合同'),
        icon: 'icon-is-basis',
        iconActive: 'icon-is-contract-active',
        path: '',
        children: [
          {
            label: $t('审查合同'),
            path: '/contract',
            permission: getPermission('/contract'),
          },
          {
            label: $t('文本比对'),
            path: '/createComparison',
            permission: getPermission('/createComparison'),
          },
          {
            label: $t('信息抽取'),
            path: '/extraction',
            permission: getPermission('/extraction'),
          },
        ],
      },
      {
        label: $t('问答'),
        icon: 'icon-is-chat',
        iconActive: 'icon-is-chat-active',
        path: '/chat',
        permission: getPermission('/chat'),
      },
    ]
  } else {
    baseMenus = [
      {
        label: $t('合同'),
        icon: 'icon-is-basis',
        iconActive: 'icon-is-contract-active',
        path: '/contract',
        permission: getPermission('/contract'),
      },
      {
        label: $t('文本比对'),
        icon: 'icon-is-comparison',
        iconActive: 'icon-is-compare',
        path: '/createComparison',
        permission: getPermission('/createComparison'),
      },
      {
        label: $t('信息抽取'),
        icon: 'icon-is-taizhang',
        iconActive: 'icon-is-taizhang2',
        path: '/extraction',
        permission: getPermission('/extraction'),
      },
      {
        label: $t('问答'),
        icon: 'icon-is-chat',
        iconActive: 'icon-is-chat-active',
        path: '/chat',
        permission: getPermission('/chat'),
      },
    ]
  }

  return baseMenus
})

const route = useRoute()
const router = useRouter()

// 统一的路径匹配逻辑，用于菜单高亮
const getActiveMenuPath = (path: string) => {
  if (path.startsWith('/contract')) return '/contract'
  if (path.startsWith('/chat')) return '/chat'
  return path
}

const defaultActive = computed(() => getActiveMenuPath(route.path))
const historyChatList = computed(() => chatStore.historyChatList.slice(0, 5))
const currentMenu = computed(() => getActiveMenuPath(route.path))

// 监听路由变化，用于调试菜单高亮
watch(
  () => route.path,
  (newPath) => {
    console.log('路由变化:', newPath)
    console.log('菜单高亮路径:', defaultActive.value)
    console.log('当前菜单:', currentMenu.value)
  },
)

const finishEditName = (params: { id: string; chatTitle: string }) => {
  chatStore.historyChatList.forEach((item) => {
    if (item.id === params.id) {
      item.chatTitle = params.chatTitle
    }
  })
}

const finishDeleteChat = async (id: string) => {
  await chatStore.deleteChatsFromStore(id)
}

// 处理用户菜单点击
const handleUserMenuClick = () => {
  if (userStore.userInfo?.nickName) {
    // 已登录用户：切换菜单显示状态
    showUserMenu.value = !showUserMenu.value
  } else {
    // 未登录用户：显示登录框
    console.log('登录')
    userStore.setShowLoginBox(true)
  }
}

// 处理菜单关闭
function handleMenuClose() {
  showUserMenu.value = false
  console.log('showUserMenu: `11`', showUserMenu)
}

// 处理版本切换
function handleVersionSwitch() {
  showUserMenu.value = false
}

// 点击外部关闭菜单
function handleClickOutside() {
  if (showUserMenu.value) {
    showUserMenu.value = false
  }
}

const toHistoryChat = () => {
  router?.push('/history-chat')
}

// 监听全局点击事件
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

const isFold = ref(false) // 侧边栏折叠状态
const handleChangeFold = (val: boolean) => {
  isFold.value = val
}

async function getChats() {
  const {
    code,
    data: { list },
  } = await ChatApi.getChatsList<IChatsListResponse>({ currentPageNo: pageNo, pageSize })
  if (RESPONSE_CODE_SUCCESS == code && list) {
    const tempList: ChatItem[] = []
    list.forEach((it: IChatItem) => {
      tempList.push(ChatItem.create(it))
    })
    chatStore.setHistoryChatList(tempList)
  }
}

defineExpose({
  getChats,
})
</script>

<template>
  <div>
    <Transition name="fade">
      <div v-if="!isFold" class="sidebar">
        <div class="logo">
          <Icons name="slogan" width="8.125rem" height="2.375rem" />
          <i class="iconfont icon-is-fold" @click="handleChangeFold(true)"></i>
        </div>
        <el-scrollbar wrap-class="scrollbar-wrapper">
          <el-menu :default-active="defaultActive" class="el-menu-vertical-demo" router>
            <template v-for="menu of menus" :key="menu.path">
              <el-sub-menu :index="menu.path" v-if="menu.children && menu.children.length">
                <template #title>
                  <i :class="currentMenu == menu.path ? menu.iconActive : menu.icon" class="iconfont"></i>
                  <span class="menu-title">{{ menu.label }}</span>
                </template>
                <el-menu-item-group>
                  <el-menu-item
                    :index="childMenu.path"
                    v-for="childMenu of menu.children"
                    :key="childMenu.path"
                    v-auth="childMenu.permission"
                  >
                    <span class="menu-title-sub">{{ childMenu.label }}</span>
                  </el-menu-item>
                </el-menu-item-group>
              </el-sub-menu>
              <el-menu-item :index="menu.path" v-else class="menu-wrap" v-auth="menu.permission">
                <template #title>
                  <i :class="currentMenu == menu.path ? menu.iconActive : menu.icon" class="iconfont"></i>
                  <span class="menu-title">{{ menu.label }}</span>
                </template>
              </el-menu-item>
            </template>
          </el-menu>
          <div class="chat-history" v-auth="'QA'">
            <div class="chat-history-header">
              <i class="iconfont icon-chat-history">&#xe653;</i>
              <span class="chat-history-title">{{ $t('历史问答') }}</span>
            </div>
            <div v-if="userStore.getToken()" class="chat-history-list">
              <recentChatItem
                v-for="(item, index) in historyChatList"
                :key="index"
                :chat-item="item"
                @edit-name="finishEditName"
                @delete-chat="finishDeleteChat"
              ></recentChatItem>
              <div
                v-if="historyChatList.length > 0"
                class="check-all"
                :class="route.path.includes('recents') ? 'active' : ''"
                @click.stop="toHistoryChat"
              >
                <span class="chat-title">{{ $t('查看全部') }}</span>
              </div>
            </div>
          </div>
        </el-scrollbar>
        <div class="footer" @click.stop="handleUserMenuClick">
          <div class="user-info">
            <div class="footer-icon">
              <img
                v-show="userStore.userInfo?.memberInfo"
                src="../../assets/images/org.png"
                style="width: 32px; height: 32px"
                alt=""
              />
              <img
                v-show="!userStore.userInfo?.memberInfo"
                src="../../assets/images/user.png"
                style="width: 32px; height: 32px"
                alt=""
              />
            </div>
            <div style="flex: 1; min-width: 0">
              <span class="footer-name">{{
                userStore.userInfo?.memberInfo
                  ? userStore.userInfo?.memberInfo.realName
                  : userStore.userInfo?.nickName || $t('请登录')
              }}</span>
              <div v-if="userStore.userInfo?.memberInfo" class="user-corp-id">
                {{ userStore.userInfo.memberInfo.corp?.corpName || '-' }}
              </div>
            </div>
          </div>
          <!-- 用户菜单浮层 -->
          <UserMenu
            :visible="showUserMenu"
            :permissionList="permissionList"
            @close="handleMenuClose"
            @version-switch="handleVersionSwitch"
          />
        </div>
      </div>
    </Transition>
    <!-- 折叠状态的展开按钮 -->
    <div v-show="isFold" class="sidebar-menus" @click="handleChangeFold(false)">
      <div class="sidebar-menus-icon">
        <i class="iconfont icon-is-unfold"></i>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.sidebar {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 220px;
  height: 100vh;
  padding: 0 0 16px;
  white-space: nowrap;
  background-color: var(--sidebar-bg);

  // logo
  .logo {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 15px 12px 16px;
  }

  // 菜单列表
  .scrollbar-wrapper {
    flex: 1;
    width: 100%;
    .menu-title {
      margin-left: 8px;
      &-sub {
        margin-left: 4px;
      }
      .menu-wrap {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        min-height: 40px;
        padding-left: 10px;
        margin-bottom: 4px;
        cursor: pointer;
        border-radius: 4px;
        &:hover {
          background-color: var(--menu-bg-active);
        }
      }
    }
    .chat-history {
      margin-top: 0.25rem;
      .chat-history-header {
        display: flex;
        align-items: center;
        height: 2rem;
        padding-left: 0.5rem;
        .chat-history-title {
          margin-left: 0.5rem;
          font-size: 0.875rem;
        }
      }
      &-list {
        display: flex;
        flex-direction: column;
        .check-all {
          display: flex; /* 确保内容垂直居中 */
          align-items: center; /* 垂直居中 */
          height: 2.5rem; /* 保持高度为 40px */
          padding-left: 0.5rem;
          margin: 0.25rem 0;
          margin-left: 1.5rem;
          overflow: hidden;
          font-size: 0.875rem;
          font-weight: 400;
          line-height: 2.5rem;
          color: var(--iterms-sub-text-color);
          cursor: pointer;
        }
        .check-all:hover {
          background-color: var(--input-bg);
          border-radius: 0.25rem;
        }
        .active {
          display: flex;
          color: var(--main-font);
          background-color: rgb(73 46 209 / 7%);
          border-radius: 0.25rem;
        }
      }
    }
  }

  // 底部菜单
  .footer {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 3rem;
    cursor: pointer;
    transition: all 0.3s ease;
    .user-info {
      display: flex;
      flex: 1;
      align-items: center;
      min-width: 0; // 防止文字溢出
      height: 40px;
      padding-left: 0.25rem;
      margin: 0.75rem;
      border-radius: 0.25rem;
      &:hover {
        background-color: var(--menu-bg-active);
      }
    }
    .footer-icon {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      width: 2rem;
      height: 2rem;
      margin-right: 0.5rem;
      font-size: 0.875rem;
      font-weight: 400;
      color: #31226e;
      border-radius: 50%;
    }
    .footer-name {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--minor-font);
      white-space: nowrap;
    }
    .user-corp-id {
      width: 100%;
      margin-top: 2px;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 0.625rem;
      line-height: 0.875rem;
      color: #888;
      white-space: nowrap;
    }
  }

  // 图片 //////// 待完善
  .balance {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 1;
    width: 84px;
    height: 104px;
  }
}

// 图标样式
.iconfont {
  cursor: pointer;
}
.icon-is-unfold,
.icon-is-fold {
  font-size: 22px;
}

// 折叠按钮
.sidebar-menus {
  position: fixed;
  top: 8px;
  left: 0;
  width: 16px;
  height: 40px;
  transition: width 0.2s linear;
  &-icon {
    position: absolute;
    right: 0;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--bg-color);
    box-shadow: 0 4px 12px 0 rgb(34 29 57 / 10%);
  }
  &:hover {
    z-index: 5;
    width: 50px;
    color: var(--main-font);
  }
}

// 折叠动画效果
.fade-enter-active,
.fade-leave-active {
  transition: all 0.2s linear;
}
.fade-enter-from,
.fade-leave-to {
  width: 0;
  opacity: 0;
}

// 菜单列表组件
:deep(.el-scrollbar) {
  width: 100%;
  height: 100%;
  padding: 0 12px;
}
:deep(.el-menu) {
  background-color: var(--sidebar-bg);
  border-right: none;
}
:deep(.el-sub-menu__title) {
  height: 40px;
  padding: 0 0 0 8px !important;
  margin-bottom: 8px;
  line-height: 40px;
  background-color: var(--sidebar-bg);
  &:hover {
    background: var(--menu-bg-active);
  }
}
:deep(.el-menu-item-group__title) {
  display: none;
}
:deep(.el-sub-menu__icon-arrow) {
  right: 6px;
  font-size: 14px;
}
:deep(.el-menu-item) {
  height: 40px;
  padding: 0 0 0 28px !important;
  margin-bottom: 8px;
  line-height: 40px;
  background-color: var(--sidebar-bg);
  border-radius: 4px;
  &.is-active {
    color: var(--main-font);
    background-color: var(--menu-bg-active);
  }
  &:hover {
    background: var(--menu-bg-active);
  }
}
:deep(.menu-wrap.el-menu-item) {
  padding-left: 10px !important;
}
</style>
