/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Attach: typeof import('./src/components/chat/attach.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElMenuItemGroup: typeof import('element-plus/es')['ElMenuItemGroup']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    HandleBox: typeof import('./src/components/handleBox/index.vue')['default']
    Icons: typeof import('./src/components/Icons.vue')['default']
    LoginDialog: typeof import('./src/components/LoginDialog.vue')['default']
    OOffice: typeof import('./src/components/ops/OOffice.vue')['default']
    PageLayout: typeof import('./src/components/PageLayout.vue')['default']
    RecentChatItem: typeof import('./src/components/chat/recentChatItem.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TextInputer: typeof import('./src/components/chat/textInputer.vue')['default']
    TextOverTip: typeof import('./src/components/TextOverTip.vue')['default']
    Wps: typeof import('./src/components/wps/index.vue')['default']
  }
  export interface GlobalDirectives {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
