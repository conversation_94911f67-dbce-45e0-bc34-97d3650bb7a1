import { $t } from '@/utils/i18n'
import type { AxiosResponse } from 'axios'
export function getQueryPath(name: string): string {
  const fullPath = location.search
  if (!fullPath.split(`${name}=`)[1]) return ''
  return decodeURIComponent(fullPath.split(`${name}=`)[1])
}

/**
 * 将 rem 单位转换为像素值 (px)
 *
 * @param rem - 要转换的 rem 值，可以是数字或带单位的字符串 (如 '1.5rem')
 * @returns 转换后的像素值字符串，格式为 "Xpx"
 *
 * @example
 * // 假设根字体大小为 16px
 * remToPx('2rem') => "32px"
 * remToPx(1.5) => "24px"
 */
export function remToPx(rem: string | number) {
  // 如果输入是字符串，则提取数值部分
  const remValue = typeof rem === 'string' ? parseFloat(rem) : rem
  // 获取文档根元素的字体大小，这是 rem 单位的基础
  const rootFontSize = parseFloat(getComputedStyle(document.documentElement).fontSize)
  // 计算像素值并返回带单位的字符串
  return `${remValue * rootFontSize}px`
}

// export function formatMD(str: string) {
//   const reg = /(\*+)\\n+/g
//   str = str.replace(reg, '$1')

//   const reg2 = /\s+(\\n+)/g
//   str = str.replace(reg2, '$1')

//   const reg3 = /(#+)([^\\n#])/g
//   str = str.replace(reg3, '$1 $2')
//   return str
// }

export function formatMD(str: string) {
  const reg1 = /(\*+)(\s*)(.+?)(\s*)(\*+)/g
  str = str.replace(reg1, '$1$3$5')

  const reg2 = /(#+)([^\s#])/g
  str = str.replace(reg2, '$1 $2')

  const reg3 = /([^\\n#])(#+)/g
  str = str.replace(reg3, '\n$2')

  const reg4 = /^```markdown/g
  str = str.replace(reg4, '')

  const reg5 = /```$/g
  str = str.replace(reg5, '')

  return str
}

export function uuid() {
  const s: string[] = []
  const hexDigits = '0123456789abcdef'
  for (let i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
  }
  s[14] = '4'
  s[19] = hexDigits.substr((+s[19] & 0x3) | 0x8, 1)
  s[8] = s[13] = s[18] = s[23] = '-'
  return s.join('')
}

export function formatFileSize(bytes: number, decimals: number = 2): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

export function getStringLengthComplex(str: string): number {
  let length = 0
  for (let i = 0; i < str.length; i++) {
    // 中文的 Unicode 范围大致在 0x4e00 到 0x9fa5 之间
    if (/[\u4e00-\u9fa5]/.test(str[i])) {
      length += 2
    } else {
      length += 1
    }
  }
  return length
}

// 复制文本到剪贴板;
export function copyToClipboard(text: string) {
  const textArea = document.createElement('textarea')
  textArea.value = text
  textArea.style.position = 'absolute'
  textArea.style.left = '-1000px'
  document.body.appendChild(textArea)
  textArea.select()
  document.execCommand('copy')
  document.body.removeChild(textArea)
}

/**
 *
 * 所有的时间格式转化为 yyyy-MM-dd HH:mm:ss
 * @param {*} obj
 */
export function formatTime(T: string | Date): string {
  return new Date(+new Date(T) + 8 * 3600 * 1000)
    .toISOString()
    .replace(/T/g, ' ')
    .replace(/\.[\d]{3}Z/, '')
}

// 下载文件
export function exportUrlFile(fileUrl: string, fileName: string) {
  const downloadElement = document.createElement('a')
  downloadElement.style.display = 'hidden'
  downloadElement.setAttribute('download', fileName)
  downloadElement.setAttribute('href', fileUrl)
  document.body.appendChild(downloadElement)
  downloadElement.click()
  document.body.removeChild(downloadElement) // 下载完成移除元素
}

/**
 * 从 FormData 中提取指定字段的 File 并返回对应的 Blob
 * @param formData 要处理的 FormData 对象
 * @param fieldName FormData 中存放文件的字段名（默认是 "file"）
 * @returns Blob 对象（如果存在），否则返回 null
 */
export function getBlobFromFormData(formData: FormData, fieldName = 'file'): Blob | undefined {
  const file = formData.get(fieldName)
  if (file instanceof File) {
    // 可直接作为 Blob 使用
    return new Blob([file], { type: file.type })
  }
  return undefined
}

export function cloneObject(obj) {
  return JSON.parse(JSON.stringify(obj))
}

export function pConfirm(msg, title = '') {
  return new Promise((resolve) => {
    ElMessageBox.confirm(`${msg || $t('确认继续？')}`, {
      type: 'warning',
      title: title,
    })
      .then(() => {
        resolve(true)
      })
      .catch(() => {
        resolve(false)
      })
  })
}

export function isOwnAuth(code) {
  // return store.getters.userCode === code;
  return true // TODO: 需要根据实际的权限逻辑进行调整
}

type Stream = Blob

declare global {
  interface Navigator {
    msSaveBlob?: (blob: any, defaultName?: string) => boolean
  }
}

export function exportFile(data: Stream, fileName: string): void {
  const blob = new Blob([data])

  if (window.navigator.msSaveBlob) {
    try {
      window.navigator.msSaveBlob(blob, fileName)
    } catch (e) {
      console.log(e)
    }
  } else {
    const downloadElement: HTMLAnchorElement = document.createElement('a')
    const href: string = window.URL.createObjectURL(blob) // 创建下载的链接
    downloadElement.href = href
    downloadElement.download = fileName // 下载后文件名
    document.body.appendChild(downloadElement)
    downloadElement.click() // 点击下载
    document.body.removeChild(downloadElement) // 下载完成移除元素
    window.URL.revokeObjectURL(href) // 释放掉blob对象
  }
}

export function exportStream(response: AxiosResponse<Stream>, fileName?: string) {
  if (!fileName) {
    fileName = response.headers['content-disposition']?.split(';')[1]?.split('=')[1] || ''
    fileName = decodeURI(fileName as string)
  }
  exportFile(response.data, fileName)
}
