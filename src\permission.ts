import router from '@/router'
import { useAppStore } from '@/stores/app'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { useUserStore } from '@/stores'

export default {
  install: () => {
    const appStore = useAppStore()
    const userStore = useUserStore()
    NProgress.configure({ showSpinner: false }) // NProgress Configuration

    router.beforeEach(async (to, from, next) => {
      // start progress bar
      NProgress.start()
      appStore.clearAxiosCancelTokens()
      const token = userStore.token
      if (to.name === 'qyAuth') {
        return next()
      }
      if (!token && to.path !== '/contract') {
        userStore.setShowLoginBox(true)
      }

      /* has no token*/
      next()
    })

    router.afterEach(() => {
      // finish progress bar
      NProgress.done()
    })
  },
}
