<template>
  <div id="officeFrame" ref="officeFrame" class="office-frame"></div>
</template>
<script lang="ts" setup>
import mitt from '@/utils/eventsBus'
import WebOfficeSDK from './web-office-sdk-solution-v2.0.7.es.js'
import { queryWpsAppId } from '@/services/onlineOffice.js'

import { $t } from '@/utils/i18n'
const officeFrame = ref()
const instance = shallowRef()
const props = defineProps({
  fileId: {
    type: String,
    required: true,
  },
  token: {
    type: String,
    required: true,
  },
  appId: {
    type: String,
    required: true,
  },
  officeType: {
    type: String,
    required: true,
  },
  isRevison: {
    type: Boolean,
    required: true,
  },
})

const emits = defineEmits(['change'])

const defaultOptions = {
  officeType: WebOfficeSDK.OfficeType.Writer,
  appId: props.appId,
  // fileId: '1939956188603981825',
  // token: 'fef26203-a371-47e6-9771-6a8c9a4596cc',

  wpsOptions: {
    isShowDocMap: true,
    isBestScale: true,
  },
  commonOptions: {
    isBrowserViewFullscreen: false,
    isIframeViewFullscreen: false,
  },
  wordOptions: {
    isShowBottomStatusBar: true,
    isShowDocMap: true,
    showFontDownloadNotice: false,
  },
  commandBars: [
    {
      cmbId: 'HeaderLeft', // 组件 ID
      attributes: {
        visible: false, // 隐藏组件
        enable: false, // 禁用组件，组件显示但不响应点击事件
      },
    },
  ],
}
enum OfficeEvts {
  /**
   * 文档打开成功
   */
  DocumentOpenSuccess = 'DocumentOpenSuccess',
  /**
   * 文档打开失败
   */
  DocumentOpenFail = 'DocumentOpenFail',
  /**
   * 文档保存成功
   */
  DocumentSaveSuccess = 'DocumentSaveSuccess',
  /**
   * 文档保存失败
   */
  DocumentSaveFail = 'DocumentSaveFail',
  /**
   * 鼠标选区改变
   */
  SelectionChangeForDoc = 'selectionChangeForDoc', // office type:docs, wps
  MouseFocusForDoc = 'MouseFocus',
  SelectionChangeForPdf = 'selectionChangeForPdf',
  CurrentPageChange = 'CurrentPageChange',
  ContentChange = 'ContentChange',
  ClipboardCopy = 'ClipboardCopy',
  Ready = 'Ready',
  LocationByText = 'LocationByText',
  WpsReplaceText = 'WpsReplaceText',
}
const getAppId = async () => {
  const { data } = await queryWpsAppId<{ wpsAppId: string }>()
  defaultOptions.appId = data.wpsAppId
}
const getApp = () => {
  return instance.value.Application
}

const getActiveDocument = () => {
  return getApp().ActiveDocument
}

const find = async (text: string) => {
  const findResult = (await getActiveDocument().Find.Execute(text)) as Array<{ pos: number; len: number }>
  return findResult.length > 0 ? findResult[0] : { pos: 0, len: 0 } // {pos}
}

const locationByText = async (text: string) => {
  const { pos, len } = await find(text)
  if (!pos) return false
  await getActiveDocument().Range.SetRange(pos, pos + len)
  await getActiveDocument().Find.ClearHitHighlight()
  return true
}

const location = async (pos: any) => {
  const range = await getActiveDocument().Range.SetRange(pos, pos)
  if (getActiveDocument().ActiveWidnow) await getActiveDocument().ActiveWindow.ScrollIntoView(range)
}

const replaceText = async (text: string, replaceText: string) => {
  const { pos, len } = await find(text)
  const range = await getActiveDocument().Range(pos, pos + len)
  // const getText = await range.Text
  range.Text = replaceText
  await find(replaceText)
}

async function setRevisions(location: { text: string; replaceText: string }) {
  const res = await getActiveDocument().Find.Execute(location.text)
  if (!res.length) return 0
  const start = res[0].pos
  const end = start + res[0].len

  const range = await getActiveDocument().Range(start, end)
  await range.SetRange(start, end)
  await getActiveDocument().Find.ClearHitHighlight()
  range.Text = location.replaceText
  const timestamp = Math.floor(Date.now() / 1000)
  return timestamp
}

async function setSelectRevisions(replaceText: string) {
  const selection = await getActiveDocument().ActiveWindow.Selection
  const range = await selection.Range
  // const text = await range.Text
  // console.log('selection', text)
  // if (!text) return 0
  range.Text = replaceText
  const timestamp = Math.floor(Date.now() / 1000)
  return timestamp
}

async function rejectRevisions(id: number) {
  const revisions = await getActiveDocument().Revisions
  const revisionData = await revisions.Json()
  const list: number[] = []
  revisionData.forEach((item: any, idx: number) => {
    if (
      item.date === id ||
      item.date === id + 1 ||
      item.date === id + 2 ||
      item.date === id - 1 ||
      item.date === id - 2
    ) {
      list.push(idx + 1)
    }
  })
  await revisions.Item(list[0]).Reject()
  if (list.length > 1) {
    setTimeout(() => {
      rejectRevisions(id)
    }, 500)
  }
}

async function save() {
  const res = await instance.value.save()
  if (res.result === 'ok' || res.result === 'nochange') return true
  ElMessage.error($t('wps保存错误'), res.result)
  return false
}

defineExpose({
  getApp,
  getActiveDocument,
  find,
  location,
  locationByText,
  replaceText,
  setRevisions,
  setSelectRevisions,
  rejectRevisions,
  save,
})

const initOffice = async () => {
  const customOptions = {
    fileId: props.fileId,
    token: props.token,
    officeType: props.officeType == 'pdf' ? WebOfficeSDK.OfficeType.Pdf : WebOfficeSDK.OfficeType.Writer,
  }
  instance.value = WebOfficeSDK.init({
    ...defaultOptions,
    ...customOptions,
    mount: document.getElementById('officeFrame'),
  })
  instance.value.ApiEvent.AddApiEventListener('fileOpen', (data: any) => {
    // 注册在 ready() 之前
    mitt.emit(OfficeEvts.DocumentOpenSuccess, data)
  })

  await instance.value.ready()
  if (props.officeType !== 'pdf') {
    getActiveDocument().ActiveWindow.View.Zoom.PageFit = 0
    getActiveDocument().TrackRevisions = true
    const revisions = await getActiveDocument().Revisions
    revisions.RevisionsMode = 0
  }

  if (props.isRevison) {
    getActiveDocument().TrackRevisions = true
  }

  mitt.emit(OfficeEvts.Ready, 'Ready')

  installEvents()
}

const installEvents = () => {
  /**
   * 文本文档选区变化
   */

  props.officeType !== 'pdf' &&
    instance.value.ApiEvent.AddApiEventListener('WindowSelectionChange', async (data: any) => {
      const { begin, end } = data
      console.dir(data)
      if (begin === end) {
        mitt.emit(OfficeEvts.MouseFocusForDoc, data)
      } else {
        const range = await getActiveDocument().Range(begin, end)
        const text = await range.Text

        mitt.emit(OfficeEvts.SelectionChangeForDoc, { begin, end, text })
      }
    })
  /**
   * pdf文本选区变化
   */
  if (props.officeType === WebOfficeSDK.OfficeType.Pdf) {
    instance.value.ApiEvent.AddApiEventListener('TextSelectChange', (data: any) => {
      mitt.emit(OfficeEvts.SelectionChangeForPdf, data)
    })
  }

  /**
   * 监听当前页码改变事件
   */
  instance.value.ApiEvent.AddApiEventListener('CurrentPageChange', (data: any) => {
    mitt.emit(OfficeEvts.CurrentPageChange, data)
  })
  /**
   * 监听文档内容改变事件
   */
  props.officeType !== 'pdf' &&
    instance.value.ApiEvent.AddApiEventListener('ContentChange', (data: any) => {
      emits('change')
      mitt.emit(OfficeEvts.ContentChange, data)
    })
  /**
   * 监听剪切板“复制”事件
   */
  props.officeType !== 'pdf' &&
    instance.value.ApiEvent.AddApiEventListener('ClipboardCopy', (data: any) => {
      mitt.emit(OfficeEvts.ClipboardCopy, data)
    })
  mitt.on(OfficeEvts.LocationByText, (text: any) => {
    locationByText(text)
  })
  mitt.on(OfficeEvts.WpsReplaceText, (location: any) => {
    replaceText(location.text, location.replaceText)
  })
}

onMounted(async () => {
  if (!officeFrame.value) {
    ElMessage.warning('office container is null , please check')
    return
  }
  await getAppId()
  initOffice()
})
onUnmounted(() => {
  mitt.off(OfficeEvts.LocationByText)
  mitt.off(OfficeEvts.WpsReplaceText)
  instance.value?.destroy()
})
</script>
<style lang="scss" scoped>
.office-frame {
  width: 100%;
  height: 100%;
}
</style>
