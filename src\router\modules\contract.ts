import Layout from '@/layout/index.vue'
import type { RouteRecordRaw } from 'vue-router'

import { $t } from '@/utils/i18n'
const router: RouteRecordRaw[] = [
  {
    path: '/contract',
    component: Layout,
    children: [
      {
        path: '',
        name: 'Contract',
        meta: {
          fullscreen: false,
          single: false,
          title: $t('合同'),
          permission: 'contractContent',
          custom: true,
        },
        component: () => import(/* webpackChunkName: "Contract" */ '@/views/contract/index.vue'),
      },
      {
        path: '/contract/more',
        name: 'contractMore',
        meta: {
          fullscreen: true,
          single: true,
          title: $t('合同列表'),
          permission: 'contractContent',
        },
        component: () => import(/* webpackChunkName: "contract" */ '@/views/contract/more.vue'),
      },
      {
        path: '/contract/view/:status/:id',
        name: 'contractView',
        meta: {
          fullscreen: true,
          single: true,
          title: $t('合同审查'),
        },
        component: () => import(/* webpackChunkName: "contract" */ '@/views/contract/detail.vue'),
      },
      {
        path: '/contract/result/:status/:id',
        name: 'contractResult',
        meta: {
          fullscreen: true,
          single: true,
          title: $t('合同审查'),
        },
        component: () => import(/* webpackChunkName: "contract" */ '@/views/contract/detail.vue'),
      },
    ],
  },
]
export default router
