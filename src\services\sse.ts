import { PATH_URL } from '@/constants'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { useUserStore } from '@/stores'
// import session from '@/utils/session'
interface FetchConf {
  onopen?: (response: Response) => Promise<void>
  onmessage?: (ev: any) => void // 明确类型为 MessageEvent
  onclose?: () => void
  onerror?: (err: any) => number | null | undefined | void
}

/**
 * SSE 请求封装
 * @param url 请求路径
 * @param params 请求参数
 * @param config 配置项
 * @param ctrl AbortController，用于取消请求
 */
export const fetch = async (
  url: string,
  params: Record<string, any>, // 确保 params 是对象类型
  config: FetchConf,
  ctrl: AbortController,
  method = 'POST',
) => {
  const userStore = useUserStore()
  const fullUrl = encodeURI(`${PATH_URL}${url}`)

  try {
    const postConfig: any = {
      headers: {
        Accept: 'text/event-stream',
        csrf_token: userStore.getToken() || '',
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
        'X-Accel-Buffering': 'no',
      },
      signal: ctrl.signal,
      method: method,

      openWhenHidden: true,
      ...config, // 合并传入的配置项
    }
    if (method.toLocaleUpperCase() === 'POST') {
      postConfig.body = JSON.stringify(params)
    }
    // 调用 fetchEventSource
    await fetchEventSource(fullUrl, postConfig)
  } catch (error) {
    // 捕获错误并记录日志
    console.error('Error in fetchEventSource:', error)
    if (config.onerror) {
      config.onerror(error)
    }
  }
}
